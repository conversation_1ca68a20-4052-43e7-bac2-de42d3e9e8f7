# Test Data for Call Attempts Analysis V2 Endpoint

This directory contains comprehensive test data for validating the Call Attempts Analysis V2 endpoint functionality. The data is designed to provide realistic scenarios and a variety of results to thoroughly test all features.

## Data Overview

### Campaigns
- **Campaign 1**: Spring 2024 Annual Fund (Telefunding)
- **Campaign 2**: Major Donor Campaign Q2 (Telefunding) 
- **Campaign 3**: Corporate Sales Q2 2024 (Telemarketing)

### Campaign Stages & Dialing Rules
Each campaign has multiple stages with different dialing rules:
- **Quantities**: Range from 2-7 call attempts per lead
- **Time Windows**: Business hours, extended hours, evening hours, etc.
- **Date Ranges**: Active rules spanning March-June 2024

### Leads & Agent Portfolios
- **20 leads** with realistic contact information
- **8 agents** with portfolio assignments via `agentPortfolioTag`
- **Multiple skill/subskill combinations** for Telefunding and Telemarketing
- **Variety in contact methods**: Some leads have multiple phone numbers, others have gaps

### Call Attempts Distribution
Designed to create variety in dial attempt buckets:
- **0 dials**: Leads with no call attempts
- **1 dial**: Single attempt leads
- **2-4 dials**: Moderate attempt leads  
- **5-19 dials**: High attempt leads (Lead 7 has 10+ attempts)
- **20+ dials**: Not included in current dataset (can be added if needed)

### Callbacks & Suppressions
- **8 callbacks** with future end dates to test callback filtering
- **5 suppressions** across different campaigns and stages
- **Mixed expired/active status** to test filtering logic

## Expected Test Results

### Campaign 1 (Spring 2024 Annual Fund)
**Data Segmentation Hierarchy**: Campaign Stage > Reporting Group > Lead Type > Dialing Rules

#### Stage 1 (Initial Contact)
- **Annual Fund > Annual Fund New**: 3 leads, varying call attempts
- **Major Donors > Major Gift Prospects**: 2 leads with different attempt counts
- **Membership > Membership New**: 2 leads

#### Stage 2 (Follow-up) 
- **Annual Fund > Annual Fund Renewal**: 2 leads
- **Annual Fund > Annual Fund Lapsed**: 1 lead
- **Membership > Membership Renewal**: 2 leads
- **Major Donors > Major Gift Renewals**: 1 lead

#### Stage 3 (Final Appeal)
- **Annual Fund > Annual Fund New**: 1 lead
- **Annual Fund > Annual Fund Lapsed**: 1 lead
- **Major Donors > Major Gift Renewals**: 1 lead

### Agent Filtering Test Cases
When filtering by specific agents:
- **Sarah Johnson**: Should see leads 1, 3, 10, 18 (4 leads)
- **Michael Chen**: Should see leads 2, 11, 19 (3 leads)
- **Emily Rodriguez**: Should see leads 4, 12 (2 leads)
- **David Thompson**: Should see leads 5, 13, 15 (3 leads)

### Dialing Rule Filtering Test Cases
When filtering by specific dialing rules:
- **Business Hours (Rule 1)**: Multiple leads across different stages
- **Extended Hours (Rule 2)**: Different set of leads
- **Evening Hours (Rule 3)**: Subset of leads with evening calling rules

## File Import Order

Import files in this exact order to maintain referential integrity:

1. `01_campaigntypes.csv`
2. `02_skills.csv` 
3. `03_subskills.csv`
4. `04_datetimerulesets.csv`
5. `05_campaigns.csv`
6. `06_campaignstages.csv`
7. `07_campaignstagedatetimerules.csv`
8. `08_agents.csv`
9. `09_leads.csv`
10. `10_campaignleads.csv`
11. `11_callattempts.csv`
12. `12_callbacks.csv`
13. `13_suppressions.csv`

## Import Instructions

### Option 1: MySQL LOAD DATA INFILE
```sql
-- Update paths in import_test_data.sql to match your system
-- Then run:
source /path/to/test-data/import_test_data.sql
```

### Option 2: Manual Import via MySQL Workbench
1. Open MySQL Workbench
2. Right-click on each table
3. Select "Table Data Import Wizard"
4. Choose corresponding CSV file
5. Map columns and import

### Option 3: Command Line Import
```bash
# For each CSV file:
mysql -u username -p database_name -e "
LOAD DATA LOCAL INFILE 'filename.csv' 
INTO TABLE table_name 
FIELDS TERMINATED BY ',' 
ENCLOSED BY '\"' 
LINES TERMINATED BY '\n' 
IGNORE 1 ROWS;"
```

## Testing Scenarios

### 1. Campaign-Wide Analysis
```javascript
POST /campaigns/1/callattemptanalysisv2
{
  "dialingRuleId": "all",
  "agentId": null
}
```
**Expected**: All leads for Campaign 1 across all stages and dialing rules

### 2. Specific Dialing Rule
```javascript
POST /campaigns/1/callattemptanalysisv2
{
  "dialingRuleId": "550e8400-e29b-41d4-a716-446655440001",
  "agentId": null
}
```
**Expected**: Only leads with call attempts from Business Hours dialing rule

### 3. Agent Portfolio Filter
```javascript
POST /campaigns/1/callattemptanalysisv2
{
  "dialingRuleId": "all", 
  "agentId": 1
}
```
**Expected**: Only leads assigned to Sarah Johnson's portfolio

### 4. Combined Filters
```javascript
POST /campaigns/1/callattemptanalysisv2
{
  "dialingRuleId": "550e8400-e29b-41d4-a716-************",
  "agentId": 2
}
```
**Expected**: Michael Chen's portfolio leads with Extended Hours dialing rule

## Data Validation Queries

```sql
-- Verify lead distribution by agent
SELECT agentPortfolioTag, COUNT(*) as lead_count 
FROM leads 
WHERE id <= 20 
GROUP BY agentPortfolioTag;

-- Verify call attempts by dialing rule
SELECT createdFromDTUuid, COUNT(*) as attempt_count
FROM callattempts 
WHERE id <= 40
GROUP BY createdFromDTUuid;

-- Verify campaign lead assignments
SELECT campaignid, currentCampaignStageId, COUNT(*) as lead_count
FROM campaignleads 
WHERE id <= 30
GROUP BY campaignid, currentCampaignStageId;
```

## Notes

- All dates are set in 2024 to ensure rules are "active" during testing
- Phone numbers use realistic US format but are not real numbers
- Email addresses use example domains
- UUIDs for dialing rules follow standard format
- Agent portfolio tags match agent names for easy identification
- Suppressed leads are included to test filtering logic
