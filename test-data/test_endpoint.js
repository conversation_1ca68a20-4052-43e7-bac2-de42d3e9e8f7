/**
 * Test Script for Call Attempts Analysis V2 Endpoint
 * Run this after importing the test data to validate endpoint functionality
 */

const axios = require('axios');

// Configuration
const BASE_URL = 'http://localhost:3000/api'; // Update with your server URL
const CAMPAIGN_ID = 1; // Test with Campaign 1 (Spring 2024 Annual Fund)

// Test scenarios
const testScenarios = [
    {
        name: 'Campaign-Wide Analysis (All Rules, All Agents)',
        payload: {
            dialingRuleId: 'all',
            agentId: null
        }
    },
    {
        name: 'Specific Dialing Rule (Business Hours)',
        payload: {
            dialingRuleId: '550e8400-e29b-41d4-a716-446655440001',
            agentId: null
        }
    },
    {
        name: 'Agent Portfolio Filter (<PERSON>)',
        payload: {
            dialingRuleId: 'all',
            agentId: 1
        }
    },
    {
        name: 'Combined Filters (<PERSON> + Extended Hours)',
        payload: {
            dialingRuleId: '550e8400-e29b-41d4-a716-446655440002',
            agentId: 2
        }
    },
    {
        name: 'Major Donor Campaign (Campaign 2)',
        payload: {
            dialingRuleId: 'all',
            agentId: null
        },
        campaignId: 2
    },
    {
        name: 'Corporate Sales Campaign (Campaign 3)',
        payload: {
            dialingRuleId: 'all',
            agentId: null
        },
        campaignId: 3
    }
];

async function runTest(scenario) {
    const campaignId = scenario.campaignId || CAMPAIGN_ID;
    const url = `${BASE_URL}/campaigns/${campaignId}/callattemptanalysisv2`;
    
    console.log(`\n🧪 Testing: ${scenario.name}`);
    console.log(`📡 POST ${url}`);
    console.log(`📦 Payload:`, JSON.stringify(scenario.payload, null, 2));
    
    try {
        const response = await axios.post(url, scenario.payload);
        const data = response.data;
        
        console.log(`✅ Status: ${response.status}`);
        console.log(`📊 Results Summary:`);
        console.log(`   - Total Leads: ${data.totalLeads || 0}`);
        console.log(`   - Total Call Attempts: ${data.totalCallAttempts || 0}`);
        console.log(`   - Total Viable: ${data.totalViable || 0}`);
        console.log(`   - Total Exhausted: ${data.totalExhausted || 0}`);
        console.log(`   - Total Callbacks: ${data.totalCallback || 0}`);
        console.log(`   - Stages Count: ${data.stages ? data.stages.length : 0}`);
        
        if (data.stages && data.stages.length > 0) {
            console.log(`📋 Stage Breakdown:`);
            data.stages.forEach((stage, index) => {
                console.log(`   Stage ${index + 1}: ${stage.reportingGroups ? stage.reportingGroups.length : 0} reporting groups`);
            });
        }
        
        if (data.totalDialAttemptBuckets) {
            console.log(`🪣 Dial Attempt Buckets:`);
            console.log(`   - 0 dials: ${data.totalDialAttemptBuckets.zero || 0}`);
            console.log(`   - 1 dial: ${data.totalDialAttemptBuckets.one || 0}`);
            console.log(`   - 2-4 dials: ${data.totalDialAttemptBuckets.twoToFour || 0}`);
            console.log(`   - 5-19 dials: ${data.totalDialAttemptBuckets.fiveToNineteen || 0}`);
            console.log(`   - 20+ dials: ${data.totalDialAttemptBuckets.twentyPlus || 0}`);
        }
        
        return { success: true, data };
        
    } catch (error) {
        console.log(`❌ Error: ${error.response ? error.response.status : 'Network Error'}`);
        if (error.response && error.response.data) {
            console.log(`📝 Error Details:`, error.response.data);
        } else {
            console.log(`📝 Error Message:`, error.message);
        }
        return { success: false, error };
    }
}

async function runAllTests() {
    console.log('🚀 Starting Call Attempts Analysis V2 Endpoint Tests');
    console.log('=' .repeat(60));
    
    const results = [];
    
    for (const scenario of testScenarios) {
        const result = await runTest(scenario);
        results.push({ scenario: scenario.name, ...result });
        
        // Add delay between tests
        await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    console.log('\n' + '=' .repeat(60));
    console.log('📈 Test Summary:');
    
    const successful = results.filter(r => r.success).length;
    const failed = results.filter(r => !r.success).length;
    
    console.log(`✅ Successful: ${successful}`);
    console.log(`❌ Failed: ${failed}`);
    
    if (failed > 0) {
        console.log('\n❌ Failed Tests:');
        results.filter(r => !r.success).forEach(r => {
            console.log(`   - ${r.scenario}`);
        });
    }
    
    console.log('\n🎯 Test Complete!');
}

// Run tests if this file is executed directly
if (require.main === module) {
    runAllTests().catch(console.error);
}

module.exports = { runTest, runAllTests };
