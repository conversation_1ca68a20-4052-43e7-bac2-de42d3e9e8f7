-- Import Test Data for Call Attempts Analysis V2 Endpoint
-- Run these commands in MySQL to import the CSV files

-- Disable foreign key checks temporarily
SET FOREIGN_KEY_CHECKS = 0;

-- Clear existing test data (optional - be careful in production!)
-- DELETE FROM suppressions WHERE id BETWEEN 1 AND 100;
-- DELETE FROM callbacks WHERE id BETWEEN 1 AND 100;
-- DELETE FROM callattempts WHERE id BETWEEN 1 AND 100;
-- <PERSON><PERSON><PERSON> FROM campaignleads WHERE id BETWEEN 1 AND 100;
-- DELETE FROM campaignstagedatetimerules WHERE id BETWEEN 1 AND 100;
-- <PERSON><PERSON><PERSON> FROM campaignstages WHERE id BETWEEN 1 AND 100;
-- DELE<PERSON> FROM campaigns WHERE id BETWEEN 1 AND 100;
-- DELETE FROM agents WHERE id BETWEEN 1 AND 100;
-- <PERSON><PERSON><PERSON> FROM leads WHERE id BETWEEN 1 AND 100;
-- DELETE FROM subskills WHERE id BETWEEN 1 AND 100;
-- <PERSON>LE<PERSON> FROM skills WHERE id BETWEEN 1 AND 100;
-- DELETE FROM datetimerulesets WHERE id BETWEEN 1 AND 100;
-- DELETE FROM campaigntypes WHERE id BETWEEN 1 AND 100;

-- Import data in dependency order
-- LOAD DATA INFILE '/path/to/test-data/01_campaigntypes.csv'
-- INTO TABLE campaigntypes
-- FIELDS TERMINATED BY ','
-- ENCLOSED BY '"'
-- LINES TERMINATED BY '\n'
-- IGNORE 1 ROWS;

-- LOAD DATA INFILE '/path/to/test-data/02_skills.csv'
-- INTO TABLE skills
-- FIELDS TERMINATED BY ','
-- ENCLOSED BY '"'
-- LINES TERMINATED BY '\n'
-- IGNORE 1 ROWS;

-- LOAD DATA INFILE '/path/to/test-data/03_subskills.csv'
-- INTO TABLE subskills
-- FIELDS TERMINATED BY ','
-- ENCLOSED BY '"'
-- LINES TERMINATED BY '\n'
-- IGNORE 1 ROWS;

LOAD DATA INFILE '/path/to/test-data/04_datetimerulesets.csv'
INTO TABLE datetimerulesets
FIELDS TERMINATED BY ','
ENCLOSED BY '"'
LINES TERMINATED BY '\n'
IGNORE 1 ROWS;

-- LOAD DATA INFILE '/path/to/test-data/05_campaigns.csv'
-- INTO TABLE campaigns
-- FIELDS TERMINATED BY ','
-- ENCLOSED BY '"'
-- LINES TERMINATED BY '\n'
-- IGNORE 1 ROWS;

-- LOAD DATA INFILE '/path/to/test-data/06_campaignstages.csv'
-- INTO TABLE campaignstages
-- FIELDS TERMINATED BY ','
-- ENCLOSED BY '"'
-- LINES TERMINATED BY '\n'
-- IGNORE 1 ROWS;

LOAD DATA INFILE '/path/to/test-data/07_campaignstagedatetimerules.csv'
INTO TABLE campaignstagedatetimerules
FIELDS TERMINATED BY ','
ENCLOSED BY '"'
LINES TERMINATED BY '\n'
IGNORE 1 ROWS;

-- LOAD DATA INFILE '/path/to/test-data/08_agents.csv'
-- INTO TABLE agents
-- FIELDS TERMINATED BY ','
-- ENCLOSED BY '"'
-- LINES TERMINATED BY '\n'
-- IGNORE 1 ROWS;

-- LOAD DATA INFILE '/path/to/test-data/09_leads.csv'
-- INTO TABLE leads
-- FIELDS TERMINATED BY ','
-- ENCLOSED BY '"'
-- LINES TERMINATED BY '\n'
-- IGNORE 1 ROWS;

LOAD DATA INFILE '/path/to/test-data/10_campaignleads.csv'
INTO TABLE campaignleads
FIELDS TERMINATED BY ','
ENCLOSED BY '"'
LINES TERMINATED BY '\n'
IGNORE 1 ROWS;

LOAD DATA INFILE '/path/to/test-data/11_callattempts.csv'
INTO TABLE callattempts
FIELDS TERMINATED BY ','
ENCLOSED BY '"'
LINES TERMINATED BY '\n'
IGNORE 1 ROWS;

LOAD DATA INFILE '/path/to/test-data/12_callbacks.csv'
INTO TABLE callbacks
FIELDS TERMINATED BY ','
ENCLOSED BY '"'
LINES TERMINATED BY '\n'
IGNORE 1 ROWS;

LOAD DATA INFILE '/path/to/test-data/13_suppressions.csv'
INTO TABLE suppressions
FIELDS TERMINATED BY ','
ENCLOSED BY '"'
LINES TERMINATED BY '\n'
IGNORE 1 ROWS;

-- Re-enable foreign key checks
SET FOREIGN_KEY_CHECKS = 1;

-- Verify data import
SELECT 'Campaign Types' as table_name, COUNT(*) as record_count FROM campaigntypes WHERE id <= 100
UNION ALL
SELECT 'Skills', COUNT(*) FROM skills WHERE id <= 100
UNION ALL
SELECT 'SubSkills', COUNT(*) FROM subskills WHERE id <= 100
UNION ALL
SELECT 'DateTimeRuleSets', COUNT(*) FROM datetimerulesets WHERE id <= 100
UNION ALL
SELECT 'Campaigns', COUNT(*) FROM campaigns WHERE id <= 100
UNION ALL
SELECT 'Campaign Stages', COUNT(*) FROM campaignstages WHERE id <= 100
UNION ALL
SELECT 'Campaign Stage DateTime Rules', COUNT(*) FROM campaignstagedatetimerules WHERE id <= 100
UNION ALL
SELECT 'Agents', COUNT(*) FROM agents WHERE id <= 100
UNION ALL
SELECT 'Leads', COUNT(*) FROM leads WHERE id <= 100
UNION ALL
SELECT 'Campaign Leads', COUNT(*) FROM campaignleads WHERE id <= 100
UNION ALL
SELECT 'Call Attempts', COUNT(*) FROM callattempts WHERE id <= 100
UNION ALL
SELECT 'Callbacks', COUNT(*) FROM callbacks WHERE id <= 100
UNION ALL
SELECT 'Suppressions', COUNT(*) FROM suppressions WHERE id <= 100;
