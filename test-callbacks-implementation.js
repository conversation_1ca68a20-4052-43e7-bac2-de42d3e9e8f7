var APP_SETTINGS = require('./config/constants')
var Sequelize = require('sequelize'),
    db = new Sequelize(APP_SETTINGS.DB.schema, APP_SETTINGS.DB.user, APP_SETTINGS.DB.pass, APP_SETTINGS.DB.config)
var Models = require('./database/schema')(db, Sequelize)

console.log('=== Testing Callbacks Implementation ===')
console.log('Testing if callbacks are properly included in currentLead...')

// Test configuration - update these to valid IDs in your database
const TEST_STAGE_ID = 1
const TEST_AGENT_ID = 1

testCallbacksInclusion()

function testCallbacksInclusion() {
    console.log('\nTesting callbacks inclusion in lead data...')
    
    // First, let's find a lead that has callbacks
    Models.Lead.findOne({
        include: [{
            model: Models.Callback,
            attributes: ['id', 'startDateTime', 'phone', 'expired', 'deleted', 'campaignId'],
            include: [{
                model: Models.Agent,
                attributes: ['id', 'name']
            }, {
                model: Models.Campaign,
                attributes: ['id', 'name']
            }]
        }],
        where: {
            '$callbacks.id$': {
                $ne: null
            }
        }
    })
    .then(function(leadWithCallbacks) {
        if (leadWithCallbacks) {
            console.log('\n✓ Found lead with callbacks:')
            console.log('Lead ID:', leadWithCallbacks.id)
            console.log('Lead Name:', leadWithCallbacks.first_name, leadWithCallbacks.last_name)
            console.log('Number of callbacks:', leadWithCallbacks.callbacks ? leadWithCallbacks.callbacks.length : 0)
            
            if (leadWithCallbacks.callbacks && leadWithCallbacks.callbacks.length > 0) {
                console.log('\n✓ Callback details:')
                leadWithCallbacks.callbacks.forEach(function(callback, index) {
                    console.log(`  Callback ${index + 1}:`)
                    console.log(`    ID: ${callback.id}`)
                    console.log(`    Start Date: ${callback.startDateTime}`)
                    console.log(`    Phone: ${callback.phone}`)
                    console.log(`    Status: ${callback.expired ? 'Expired' : callback.deleted ? 'Complete' : 'Scheduled'}`)
                    console.log(`    Agent: ${callback.agent ? callback.agent.name : 'Unknown'}`)
                    console.log(`    Campaign: ${callback.campaign ? callback.campaign.name : 'Unknown'}`)
                })
            }
            
            console.log('\n✓ Test PASSED: Callbacks are properly included in lead data')
        } else {
            console.log('\n⚠ No leads with callbacks found in database')
            console.log('Creating a test callback to verify the structure...')
            
            // Let's test the structure by finding any lead and checking if callbacks property exists
            Models.Lead.findOne({
                include: [{
                    model: Models.Callback,
                    attributes: ['id', 'startDateTime', 'phone', 'expired', 'deleted', 'campaignId'],
                    include: [{
                        model: Models.Agent,
                        attributes: ['id', 'name']
                    }, {
                        model: Models.Campaign,
                        attributes: ['id', 'name']
                    }]
                }]
            })
            .then(function(anyLead) {
                if (anyLead) {
                    console.log('\n✓ Found lead for structure test:')
                    console.log('Lead ID:', anyLead.id)
                    console.log('Callbacks property exists:', anyLead.callbacks !== undefined)
                    console.log('Callbacks is array:', Array.isArray(anyLead.callbacks))
                    console.log('Number of callbacks:', anyLead.callbacks ? anyLead.callbacks.length : 0)
                    console.log('\n✓ Test PASSED: Callback structure is properly set up')
                } else {
                    console.log('\n✗ No leads found in database')
                }
            })
            .catch(function(error) {
                console.log('\n✗ Error testing lead structure:', error.message)
            })
        }
    })
    .catch(function(error) {
        console.log('\n✗ Error finding lead with callbacks:', error.message)
    })
    .finally(function() {
        console.log('\n=== Test Complete ===')
        process.exit(0)
    })
}
