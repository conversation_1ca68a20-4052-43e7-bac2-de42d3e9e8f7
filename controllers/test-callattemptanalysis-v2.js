/**
 * Test script for Call Attempts Analysis V2 endpoint
 *
 * This script can be used to test the V2 endpoint with sample data
 * and compare results with the V1 endpoint.
 *
 * Usage:
 * 1. Replace the sample data return in the V2 endpoint with actual queries
 * 2. Run this script to test both endpoints with the same parameters
 * 3. Compare the results to ensure V2 replicates V1 logic correctly
 */

const fs = require('fs')
const path = require('path')

// Load test data - both files now use the same stages structure
const campaignViewTestData = require('./callattemptanalysis.v2.test.json')
const agentFilteredTestData = require('./callattemptanalysis.v2.agent.test.json')

/**
 * Test the V2 endpoint with sample data
 * @param {Object} testParams - Test parameters
 * @returns {Object} Test results
 */
function testV2Endpoint(testParams = {}) {
    const {
        campaignId = 123,
        dialingRuleId = 'all',
        agentId = null
    } = testParams

    console.log('Testing V2 Endpoint with parameters:')
    console.log(`Campaign ID: ${campaignId}`)
    console.log(`Dialing Rule ID: ${dialingRuleId}`)
    console.log(`Agent ID: ${agentId ? agentId : 'All Agents'}`)
    console.log('---')

    // Return appropriate test data based on agent filtering
    // Both files now use the same stages structure - agent selection just filters the data
    const testData = agentId ? agentFilteredTestData : campaignViewTestData

    // Validate data structure
    const validation = validateV2DataStructure(testData)

    return {
        success: validation.isValid,
        data: testData,
        validation: validation,
        summary: generateDataSummary(testData, agentId)
    }
}

/**
 * Validate the V2 data structure matches expected format
 * @param {Object} data - Data to validate
 * @returns {Object} Validation results
 */
function validateV2DataStructure(data) {
    const errors = []
    const warnings = []

    // Check required top-level fields
    const requiredFields = [
        'totalAttempts', 'totalLeads', 'totalExhausted', 'totalViable',
        'totalCallback', 'totalDontContactUntil', 'totalBadNumbers',
        'suppressedTotal', 'totalDialAttemptBuckets'
    ]

    requiredFields.forEach(field => {
        if (data[field] === undefined) {
            errors.push(`Missing required field: ${field}`)
        }
    })

    // Always validate stages structure (same for both campaign and agent-filtered views)
    if (!data.stages || !Array.isArray(data.stages)) {
        errors.push('Missing or invalid stages array')
    } else {
        // Validate stages structure
        data.stages.forEach((stage, index) => {
            if (!stage.id || !stage.name) {
                errors.push(`Stage ${index} missing id or name`)
            }
            if (!stage.reportingGroups || !Array.isArray(stage.reportingGroups)) {
                errors.push(`Stage ${index} missing reportingGroups array`)
            } else {
                // Validate reporting groups and lead types
                stage.reportingGroups.forEach((rg, rgIndex) => {
                    if (!rg.leadTypes || !Array.isArray(rg.leadTypes)) {
                        errors.push(`Reporting group ${rgIndex} in stage ${index} missing leadTypes array`)
                    } else {
                        rg.leadTypes.forEach((lt, ltIndex) => {
                            if (!lt.dialingRules || !Array.isArray(lt.dialingRules)) {
                                warnings.push(`Lead type ${ltIndex} missing dialingRules array`)
                            }
                            if (lt.callAttemptsRemaining === undefined) {
                                warnings.push(`Lead type ${ltIndex} missing callAttemptsRemaining`)
                            }
                        })
                    }
                })
            }
        })
    }

    // Validate dial attempt buckets
    if (data.totalDialAttemptBuckets) {
        const bucketFields = ['zero', 'one', 'twoToFour', 'fiveToNineteen', 'twentyPlus']
        bucketFields.forEach(bucket => {
            if (data.totalDialAttemptBuckets[bucket] === undefined) {
                errors.push(`Missing dial attempt bucket: ${bucket}`)
            }
        })
    }

    return {
        isValid: errors.length === 0,
        errors: errors,
        warnings: warnings
    }
}

/**
 * Generate a summary of the test data
 * @param {Object} data - Test data
 * @param {string|null} agentId - Agent ID if filtering by agent
 * @returns {Object} Data summary
 */
function generateDataSummary(data, agentId) {
    const summary = {
        viewType: agentId ? 'Agent Portfolio Filtered View' : 'Campaign Stage View',
        filterType: agentId ? `Agent ${agentId}` : 'All Agents',
        totalLeads: data.totalLeads,
        totalAttempts: data.totalAttempts,
        totalViable: data.totalViable,
        totalExhausted: data.totalExhausted,
        suppressedTotal: data.suppressedTotal,
        dialAttemptDistribution: data.totalDialAttemptBuckets
    }

    // Always use stages structure (same for both campaign and agent-filtered views)
    summary.stageCount = data.stages ? data.stages.length : 0
    summary.totalReportingGroups = data.stages ?
        data.stages.reduce((sum, stage) => sum + (stage.reportingGroups ? stage.reportingGroups.length : 0), 0) : 0
    summary.totalLeadTypes = data.stages ?
        data.stages.reduce((sum, stage) =>
            sum + (stage.reportingGroups ?
                stage.reportingGroups.reduce((rgSum, rg) =>
                    rgSum + (rg.leadTypes ? rg.leadTypes.length : 0), 0) : 0), 0) : 0

    return summary
}

/**
 * Compare V1 and V2 endpoint results
 * @param {Object} v1Data - V1 endpoint results
 * @param {Object} v2Data - V2 endpoint results
 * @returns {Object} Comparison results
 */
function compareV1AndV2Results(v1Data, v2Data) {
    const differences = []
    const similarities = []

    // Compare total fields
    const totalFields = ['totalLeads', 'totalAttempts', 'totalViable', 'totalExhausted']
    totalFields.forEach(field => {
        if (v1Data[field] === v2Data[field]) {
            similarities.push(`${field}: ${v1Data[field]}`)
        } else {
            differences.push(`${field}: V1=${v1Data[field]}, V2=${v2Data[field]}`)
        }
    })

    return {
        differences: differences,
        similarities: similarities,
        matchPercentage: similarities.length / (similarities.length + differences.length) * 100
    }
}

// Export functions for use in other modules
module.exports = {
    testV2Endpoint,
    validateV2DataStructure,
    generateDataSummary,
    compareV1AndV2Results
}

// If running directly, execute tests
if (require.main === module) {
    console.log('=== Call Attempts Analysis V2 Test Suite ===\n')

    // Test campaign view (all agents)
    console.log('1. Testing Campaign View (All Agents):')
    const campaignViewResult = testV2Endpoint({ agentId: null })
    console.log('Validation:', campaignViewResult.validation.isValid ? 'PASSED' : 'FAILED')
    if (campaignViewResult.validation.errors.length > 0) {
        console.log('Errors:', campaignViewResult.validation.errors)
    }
    if (campaignViewResult.validation.warnings.length > 0) {
        console.log('Warnings:', campaignViewResult.validation.warnings)
    }
    console.log('Summary:', campaignViewResult.summary)
    console.log('')

    // Test agent filtered view
    console.log('2. Testing Agent Filtered View:')
    const agentFilteredResult = testV2Endpoint({ agentId: 'agent123' })
    console.log('Validation:', agentFilteredResult.validation.isValid ? 'PASSED' : 'FAILED')
    if (agentFilteredResult.validation.errors.length > 0) {
        console.log('Errors:', agentFilteredResult.validation.errors)
    }
    if (agentFilteredResult.validation.warnings.length > 0) {
        console.log('Warnings:', agentFilteredResult.validation.warnings)
    }
    console.log('Summary:', agentFilteredResult.summary)
    console.log('')

    console.log('=== Test Suite Complete ===')
}

// If running directly, execute tests
if (require.main === module) {
    console.log('=== Call Attempts Analysis V2 Test Suite ===\n')

    // Test campaign view (all agents)
    console.log('1. Testing Campaign View (All Agents):')
    const campaignViewResult = testV2Endpoint({ agentId: null })
    console.log('Validation:', campaignViewResult.validation.isValid ? 'PASSED' : 'FAILED')
    if (campaignViewResult.validation.errors.length > 0) {
        console.log('Errors:', campaignViewResult.validation.errors)
    }
    if (campaignViewResult.validation.warnings.length > 0) {
        console.log('Warnings:', campaignViewResult.validation.warnings)
    }
    console.log('Summary:', campaignViewResult.summary)
    console.log('')

    // Test agent filtered view
    console.log('2. Testing Agent Filtered View:')
    const agentFilteredResult = testV2Endpoint({ agentId: 'agent123' })
    console.log('Validation:', agentFilteredResult.validation.isValid ? 'PASSED' : 'FAILED')
    if (agentFilteredResult.validation.errors.length > 0) {
        console.log('Errors:', agentFilteredResult.validation.errors)
    }
    if (agentFilteredResult.validation.warnings.length > 0) {
        console.log('Warnings:', agentFilteredResult.validation.warnings)
    }
    console.log('Summary:', agentFilteredResult.summary)
    console.log('')

    console.log('=== Test Suite Complete ===')
}
