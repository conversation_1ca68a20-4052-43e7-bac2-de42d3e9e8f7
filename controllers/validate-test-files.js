#!/usr/bin/env node

/**
 * Simple validation script to verify that both V2 test JSON files
 * have the correct structure and can be used to spoof query results
 */

const fs = require('fs')
const path = require('path')

// Load test data files
let campaignTestData, agentTestData

try {
    campaignTestData = require('./callattemptanalysis.v2.test.json')
    console.log('✓ Successfully loaded callattemptanalysis.v2.test.json')
} catch (error) {
    console.error('✗ Failed to load callattemptanalysis.v2.test.json:', error.message)
    process.exit(1)
}

try {
    agentTestData = require('./callattemptanalysis.v2.agent.test.json')
    console.log('✓ Successfully loaded callattemptanalysis.v2.agent.test.json')
} catch (error) {
    console.error('✗ Failed to load callattemptanalysis.v2.agent.test.json:', error.message)
    process.exit(1)
}

/**
 * Validate that a test data object has the correct V2 structure
 */
function validateStructure(data, filename) {
    const errors = []
    
    // Check required top-level fields
    const requiredFields = [
        'stages', 'totalAttempts', 'totalLeads', 'totalExhausted', 'totalViable',
        'totalCallback', 'totalDontContactUntil', 'totalBadNumbers',
        'suppressedTotal', 'totalDialAttemptBuckets'
    ]
    
    requiredFields.forEach(field => {
        if (data[field] === undefined) {
            errors.push(`Missing required field: ${field}`)
        }
    })
    
    // Validate stages structure
    if (!data.stages || !Array.isArray(data.stages)) {
        errors.push('Missing or invalid stages array')
    } else {
        data.stages.forEach((stage, stageIndex) => {
            if (!stage.id || !stage.name) {
                errors.push(`Stage ${stageIndex} missing id or name`)
            }
            
            if (!stage.reportingGroups || !Array.isArray(stage.reportingGroups)) {
                errors.push(`Stage ${stageIndex} missing reportingGroups array`)
            } else {
                stage.reportingGroups.forEach((rg, rgIndex) => {
                    if (!rg.id || !rg.name) {
                        errors.push(`Reporting group ${rgIndex} in stage ${stageIndex} missing id or name`)
                    }
                    
                    if (!rg.leadTypes || !Array.isArray(rg.leadTypes)) {
                        errors.push(`Reporting group ${rgIndex} in stage ${stageIndex} missing leadTypes array`)
                    } else {
                        rg.leadTypes.forEach((lt, ltIndex) => {
                            if (!lt.id || !lt.name) {
                                errors.push(`Lead type ${ltIndex} missing id or name`)
                            }
                            
                            if (!lt.dialingRules || !Array.isArray(lt.dialingRules)) {
                                errors.push(`Lead type ${ltIndex} missing dialingRules array`)
                            }
                            
                            if (lt.callAttemptsRemaining === undefined) {
                                errors.push(`Lead type ${ltIndex} missing callAttemptsRemaining`)
                            }
                        })
                    }
                })
            }
        })
    }
    
    // Validate dial attempt buckets
    if (data.totalDialAttemptBuckets) {
        const bucketFields = ['zero', 'one', 'twoToFour', 'fiveToNineteen', 'twentyPlus']
        bucketFields.forEach(bucket => {
            if (data.totalDialAttemptBuckets[bucket] === undefined) {
                errors.push(`Missing dial attempt bucket: ${bucket}`)
            }
        })
    }
    
    return errors
}

console.log('\n=== Validating Test File Structures ===\n')

// Validate campaign test data
console.log('1. Validating callattemptanalysis.v2.test.json:')
const campaignErrors = validateStructure(campaignTestData, 'callattemptanalysis.v2.test.json')
if (campaignErrors.length === 0) {
    console.log('   ✓ Structure is valid')
    console.log(`   ✓ Contains ${campaignTestData.stages.length} stages`)
    const totalRGs = campaignTestData.stages.reduce((sum, stage) => sum + stage.reportingGroups.length, 0)
    console.log(`   ✓ Contains ${totalRGs} reporting groups`)
} else {
    console.log('   ✗ Structure validation failed:')
    campaignErrors.forEach(error => console.log(`     - ${error}`))
}

console.log('')

// Validate agent test data
console.log('2. Validating callattemptanalysis.v2.agent.test.json:')
const agentErrors = validateStructure(agentTestData, 'callattemptanalysis.v2.agent.test.json')
if (agentErrors.length === 0) {
    console.log('   ✓ Structure is valid')
    console.log(`   ✓ Contains ${agentTestData.stages.length} stages`)
    const totalRGs = agentTestData.stages.reduce((sum, stage) => sum + stage.reportingGroups.length, 0)
    console.log(`   ✓ Contains ${totalRGs} reporting groups`)
} else {
    console.log('   ✗ Structure validation failed:')
    agentErrors.forEach(error => console.log(`     - ${error}`))
}

console.log('')

// Summary
const totalErrors = campaignErrors.length + agentErrors.length
if (totalErrors === 0) {
    console.log('🎉 All test files are valid and ready to use for spoofing query results!')
    console.log('')
    console.log('Both files now use the same structure:')
    console.log('- Campaign Stage > Reporting Group > Lead Type > Dialing Rules')
    console.log('- Agent selection is just a data filter, not a different view structure')
    console.log('')
    console.log('You can use these files to spoof the V2 endpoint response by:')
    console.log('1. Returning campaignTestData for campaign-wide analysis')
    console.log('2. Returning agentTestData for agent-filtered analysis')
    console.log('3. Both will work with the same frontend logic')
} else {
    console.log(`❌ Found ${totalErrors} validation errors. Please fix before using.`)
    process.exit(1)
}
