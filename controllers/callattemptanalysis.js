return module.exports = {
    "totalLeads": 28269,
    "totalCallAttempts": 17897,
    "totalNoCallAttempts": 25065,
    "totalCallback": 197,
    "totalViable": 1193,
    "totalDontContactUntil": 793,
    "totalNoStage": 574,
    "totalBadNumbers": 87,
    "stages": [
        {
            "id": 1186,
            "name": "1st Appeal",
            "leadsWithAttempts": 9,
            "callAttempts": 10,
            "skills": [
                {
                    "id": 2088,
                    "name": "SUB ACQ Lapsed AHM 2024 Season Subs",
                    "callAttempts": 1,
                    "leadsWithAttempts": 1,
                    "rules": [],
                    "leads": 1179,
                    "exhausted": 1178,
                    "viable": 0,
                    "dontContactUntil": 0,
                    "callback": 5,
                    "badNumber": 0
                },
                {
                    "id": 2090,
                    "name": "SUB ACQ Lapsed AHM 2019-22 Season Subs",
                    "callAttempts": 1,
                    "leadsWithAttempts": 1,
                    "rules": [],
                    "leads": 1242,
                    "exhausted": 1241,
                    "viable": 1,
                    "dontContactUntil": 8,
                    "callback": 4,
                    "badNumber": 0
                },
                {
                    "id": 2093,
                    "name": "SUB ACQ Callback from 24/25 Campaign",
                    "callAttempts": 3,
                    "leadsWithAttempts": 3,
                    "rules": [],
                    "leads": 50,
                    "exhausted": 47,
                    "viable": 1,
                    "dontContactUntil": 8,
                    "callback": 16,
                    "badNumber": 0
                },
                {
                    "id": 2102,
                    "name": "SUB ACQ 44 STBs",
                    "callAttempts": 2,
                    "leadsWithAttempts": 1,
                    "rules": [],
                    "leads": 107,
                    "exhausted": 106,
                    "viable": 1,
                    "dontContactUntil": 1,
                    "callback": 2,
                    "badNumber": 0
                },
                {
                    "id": 2103,
                    "name": "SUB ACQ MTF24 STBs",
                    "callAttempts": 1,
                    "leadsWithAttempts": 1,
                    "rules": [],
                    "leads": 1396,
                    "exhausted": 1395,
                    "viable": 0,
                    "dontContactUntil": 16,
                    "callback": 5,
                    "badNumber": 0
                },
                {
                    "id": 2122,
                    "name": "SUB ACQ FAKEIT STBs",
                    "callAttempts": 1,
                    "leadsWithAttempts": 1,
                    "rules": [],
                    "leads": 102,
                    "exhausted": 101,
                    "viable": 1,
                    "dontContactUntil": 3,
                    "callback": 0,
                    "badNumber": 0
                },
                {
                    "id": 2124,
                    "name": "SUB ACQ IDIOT STBs",
                    "callAttempts": 1,
                    "leadsWithAttempts": 1,
                    "rules": [],
                    "leads": 2834,
                    "exhausted": 2833,
                    "viable": 1,
                    "dontContactUntil": 4,
                    "callback": 4,
                    "badNumber": 0
                },
                {
                    "id": 2036,
                    "name": "Prospect Lead",
                    "exhausted": 4,
                    "leads": 70,
                    "viable": 0,
                    "dontContactUntil": 0,
                    "callback": 0,
                    "badNumber": 66,
                    "rules": []
                },
                {
                    "id": 2049,
                    "name": "First Time Sub, Saw IDIOT",
                    "exhausted": 99,
                    "leads": 99,
                    "viable": 0,
                    "dontContactUntil": 0,
                    "callback": 0,
                    "badNumber": 0,
                    "rules": []
                },
                {
                    "id": 2050,
                    "name": "IDIOT STB",
                    "exhausted": 288,
                    "leads": 289,
                    "viable": 0,
                    "dontContactUntil": 0,
                    "callback": 1,
                    "badNumber": 1,
                    "rules": []
                },
                {
                    "id": 2051,
                    "name": "Lapsed AHM&MTF Subs",
                    "exhausted": 70,
                    "leads": 70,
                    "viable": 0,
                    "dontContactUntil": 0,
                    "callback": 0,
                    "badNumber": 0,
                    "rules": []
                },
                {
                    "id": 2052,
                    "name": "SD&A Reapproach",
                    "exhausted": 127,
                    "leads": 127,
                    "viable": 0,
                    "dontContactUntil": 0,
                    "callback": 0,
                    "badNumber": 0,
                    "rules": []
                },
                {
                    "id": 2053,
                    "name": "Lapsed Sub, Lapsed Donor",
                    "exhausted": 370,
                    "leads": 372,
                    "viable": 0,
                    "dontContactUntil": 0,
                    "callback": 0,
                    "badNumber": 2,
                    "rules": []
                },
                {
                    "id": 2054,
                    "name": "Recent STB, Lapsed Donor",
                    "exhausted": 473,
                    "leads": 474,
                    "viable": 0,
                    "dontContactUntil": 0,
                    "callback": 1,
                    "badNumber": 1,
                    "rules": []
                },
                {
                    "id": 2055,
                    "name": "Lapsed Sub, Recent Donor",
                    "exhausted": 113,
                    "leads": 113,
                    "viable": 0,
                    "dontContactUntil": 0,
                    "callback": 0,
                    "badNumber": 0,
                    "rules": []
                },
                {
                    "id": 2056,
                    "name": "Recent STB, Recent Donor",
                    "exhausted": 734,
                    "leads": 743,
                    "viable": 0,
                    "dontContactUntil": 1,
                    "callback": 0,
                    "badNumber": 9,
                    "rules": []
                },
                {
                    "id": 2057,
                    "name": "Mattress STB",
                    "exhausted": 203,
                    "leads": 204,
                    "viable": 0,
                    "dontContactUntil": 0,
                    "callback": 0,
                    "badNumber": 1,
                    "rules": []
                },
                {
                    "id": 2062,
                    "name": "LYBUNT STBs 3+",
                    "exhausted": 26,
                    "leads": 26,
                    "viable": 0,
                    "dontContactUntil": 0,
                    "callback": 1,
                    "badNumber": 0,
                    "rules": []
                },
                {
                    "id": 2068,
                    "name": "SUB ACQ Requested Info",
                    "exhausted": 1,
                    "leads": 1,
                    "viable": 0,
                    "dontContactUntil": 0,
                    "callback": 0,
                    "badNumber": 0,
                    "rules": []
                },
                {
                    "id": 2086,
                    "name": "SUB ACQ Lapsed AHM 2023 Season Subs",
                    "exhausted": 634,
                    "leads": 634,
                    "viable": 0,
                    "dontContactUntil": 2,
                    "callback": 0,
                    "badNumber": 0,
                    "rules": []
                },
                {
                    "id": 2089,
                    "name": "SUB ACQ Lapsed MTF 2019-21 Season Subs",
                    "exhausted": 378,
                    "leads": 378,
                    "viable": 0,
                    "dontContactUntil": 2,
                    "callback": 4,
                    "badNumber": 0,
                    "rules": []
                },
                {
                    "id": 2097,
                    "name": "SUB RNW 3rd Yr AHM",
                    "exhausted": 1,
                    "leads": 1,
                    "viable": 0,
                    "dontContactUntil": 0,
                    "callback": 0,
                    "badNumber": 0,
                    "rules": []
                },
                {
                    "id": 2098,
                    "name": "SUB RNW 2nd Yr AHM",
                    "exhausted": 2,
                    "leads": 2,
                    "viable": 0,
                    "dontContactUntil": 1,
                    "callback": 0,
                    "badNumber": 0,
                    "rules": []
                },
                {
                    "id": 2100,
                    "name": "SUB ACQ Ancmt EM Click Recent",
                    "exhausted": 17,
                    "leads": 17,
                    "viable": 0,
                    "dontContactUntil": 0,
                    "callback": 0,
                    "badNumber": 0,
                    "rules": []
                },
                {
                    "id": 2104,
                    "name": "SUB ACQ KDT24 STBs",
                    "exhausted": 500,
                    "leads": 500,
                    "viable": 0,
                    "dontContactUntil": 12,
                    "callback": 3,
                    "badNumber": 0,
                    "rules": []
                },
                {
                    "id": 2105,
                    "name": "SUB ACQ AHM24 STBs",
                    "exhausted": 6760,
                    "leads": 6767,
                    "viable": 0,
                    "dontContactUntil": 102,
                    "callback": 44,
                    "badNumber": 7,
                    "rules": []
                },
                {
                    "id": 2111,
                    "name": "SUB RNW 3rd Yr",
                    "exhausted": 3,
                    "leads": 3,
                    "viable": 0,
                    "dontContactUntil": 1,
                    "callback": 1,
                    "badNumber": 0,
                    "rules": []
                },
                {
                    "id": 2112,
                    "name": "SUB RNW 2nd Yr",
                    "exhausted": 2,
                    "leads": 2,
                    "viable": 0,
                    "dontContactUntil": 0,
                    "callback": 1,
                    "badNumber": 0,
                    "rules": []
                },
                {
                    "id": 2114,
                    "name": "SUB ACQ LOPI STBs",
                    "exhausted": 336,
                    "leads": 336,
                    "viable": 0,
                    "dontContactUntil": 6,
                    "callback": 3,
                    "badNumber": 0,
                    "rules": []
                },
                {
                    "id": 2115,
                    "name": "SUB ACQ 19-20 STBs with 3+",
                    "exhausted": 108,
                    "leads": 108,
                    "viable": 0,
                    "dontContactUntil": 0,
                    "callback": 3,
                    "badNumber": 0,
                    "rules": []
                },
                {
                    "id": 2116,
                    "name": "SUB ACQ 2016-20 with Recent History",
                    "exhausted": 13,
                    "leads": 13,
                    "viable": 0,
                    "dontContactUntil": 0,
                    "callback": 0,
                    "badNumber": 0,
                    "rules": []
                },
                {
                    "id": 2117,
                    "name": "SUB ACQ 2yrs in a row",
                    "exhausted": 340,
                    "leads": 340,
                    "viable": 0,
                    "dontContactUntil": 0,
                    "callback": 2,
                    "badNumber": 0,
                    "rules": []
                },
                {
                    "id": 2119,
                    "name": "SUB ACQ CATK STBs",
                    "exhausted": 66,
                    "leads": 66,
                    "viable": 0,
                    "dontContactUntil": 1,
                    "callback": 0,
                    "badNumber": 0,
                    "rules": []
                },
                {
                    "id": 2120,
                    "name": "SUB ACQ DUEL STBs",
                    "exhausted": 71,
                    "leads": 71,
                    "viable": 0,
                    "dontContactUntil": 0,
                    "callback": 0,
                    "badNumber": 0,
                    "rules": []
                },
                {
                    "id": 2121,
                    "name": "SUB ACQ ELOZ STBs",
                    "exhausted": 10,
                    "leads": 10,
                    "viable": 0,
                    "dontContactUntil": 1,
                    "callback": 0,
                    "badNumber": 0,
                    "rules": []
                },
                {
                    "id": 2123,
                    "name": "SUB ACQ HAM25 STBs",
                    "exhausted": 1,
                    "leads": 1,
                    "viable": 0,
                    "dontContactUntil": 0,
                    "callback": 0,
                    "badNumber": 0,
                    "rules": []
                },
                {
                    "id": 2125,
                    "name": "SUB ACQ JGAD STBs",
                    "exhausted": 3,
                    "leads": 3,
                    "viable": 0,
                    "dontContactUntil": 0,
                    "callback": 0,
                    "badNumber": 0,
                    "rules": []
                },
                {
                    "id": 2126,
                    "name": "SUB ACQ OLDF STBs",
                    "exhausted": 1157,
                    "leads": 1157,
                    "viable": 0,
                    "dontContactUntil": 12,
                    "callback": 7,
                    "badNumber": 0,
                    "rules": []
                },
                {
                    "id": 2127,
                    "name": "SUB ACQ MATT STBs",
                    "exhausted": 4996,
                    "leads": 4996,
                    "viable": 0,
                    "dontContactUntil": 79,
                    "callback": 35,
                    "badNumber": 0,
                    "rules": []
                },
                {
                    "id": 2131,
                    "name": "SUB RNW 25 Subs",
                    "exhausted": 29,
                    "leads": 29,
                    "viable": 0,
                    "dontContactUntil": 7,
                    "callback": 1,
                    "badNumber": 0,
                    "rules": []
                },
                {
                    "id": 2132,
                    "name": "SUB RNW AR Failed",
                    "exhausted": 114,
                    "leads": 114,
                    "viable": 0,
                    "dontContactUntil": 41,
                    "callback": 8,
                    "badNumber": 0,
                    "rules": []
                },
                {
                    "id": 2133,
                    "name": "SUB RNW 1st Yr 1CTG",
                    "exhausted": 38,
                    "leads": 38,
                    "viable": 0,
                    "dontContactUntil": 12,
                    "callback": 4,
                    "badNumber": 0,
                    "rules": []
                },
                {
                    "id": 2134,
                    "name": "SUB RNW 1st Yr Sub",
                    "exhausted": 21,
                    "leads": 21,
                    "viable": 0,
                    "dontContactUntil": 3,
                    "callback": 1,
                    "badNumber": 0,
                    "rules": []
                },
                {
                    "id": 2135,
                    "name": "SUB RNW 1st Yr AHM",
                    "exhausted": 4,
                    "leads": 4,
                    "viable": 0,
                    "dontContactUntil": 1,
                    "callback": 0,
                    "badNumber": 0,
                    "rules": []
                },
                {
                    "id": 2137,
                    "name": "SUB RNW 1st Yr MTF",
                    "exhausted": 1,
                    "leads": 1,
                    "viable": 0,
                    "dontContactUntil": 0,
                    "callback": 0,
                    "badNumber": 0,
                    "rules": []
                },
                {
                    "id": 2138,
                    "name": "SUB RNW 2nd Yr Sub",
                    "exhausted": 2,
                    "leads": 2,
                    "viable": 0,
                    "dontContactUntil": 1,
                    "callback": 0,
                    "badNumber": 0,
                    "rules": []
                },
                {
                    "id": null,
                    "name": "No lead type found",
                    "exhausted": 0,
                    "leads": 0,
                    "viable": 0,
                    "dontContactUntil": 0,
                    "callback": 0,
                    "badNumber": 0,
                    "rules": [],
                    "suppressions": 17
                }
            ],
            "leads": 25112,
            "exhausted": 25016,
            "viable": 5,
            "dontContactUntil": 325,
            "callback": 156,
            "badNumber": 87,
            "suppressions": 17
        },
        {
            "id": 1188,
            "name": "Reapproach",
            "leadsWithAttempts": 1482,
            "callAttempts": 17784,
            "skills": [
                {
                    "id": 2036,
                    "name": "Prospect Lead",
                    "callAttempts": 24,
                    "leadsWithAttempts": 2,
                    "rules": [
                        {
                            "id": "09f1557e-0bb2-434f-aac3-dcb92461c540",
                            "name": "CTG Anytime",
                            "callAttempts": 24,
                            "leadsWithAttempts": 2
                        }
                    ],
                    "leads": 2,
                    "exhausted": 0,
                    "viable": 2,
                    "dontContactUntil": 0,
                    "callback": 0,
                    "badNumber": 0
                },
                {
                    "id": 2050,
                    "name": "IDIOT STB",
                    "callAttempts": 180,
                    "leadsWithAttempts": 15,
                    "rules": [
                        {
                            "id": 15,
                            "uuid": "7d95c7a1-6e62-44eb-b666-cfbceac961ad",
                            "name": "CTG Anytime",
                            "callAttempts": 180
                        }
                    ],
                    "leads": 15,
                    "exhausted": 0,
                    "viable": 15,
                    "dontContactUntil": 0,
                    "callback": 0,
                    "badNumber": 0
                },
                {
                    "id": 2052,
                    "name": "SD&A Reapproach",
                    "callAttempts": 132,
                    "leadsWithAttempts": 11,
                    "rules": [
                        {
                            "id": 15,
                            "uuid": "61be7b8b-d595-4ed2-b5f3-856eee64cf90",
                            "name": "CTG Anytime",
                            "callAttempts": 132
                        }
                    ],
                    "leads": 11,
                    "exhausted": 0,
                    "viable": 11,
                    "dontContactUntil": 0,
                    "callback": 0,
                    "badNumber": 0
                },
                {
                    "id": 2053,
                    "name": "Lapsed Sub, Lapsed Donor",
                    "callAttempts": 216,
                    "leadsWithAttempts": 18,
                    "rules": [
                        {
                            "id": 15,
                            "uuid": "e24e2ba8-d423-43b6-aa18-8b68fa95b1e9",
                            "name": "CTG Anytime",
                            "callAttempts": 216
                        }
                    ],
                    "leads": 18,
                    "exhausted": 0,
                    "viable": 18,
                    "dontContactUntil": 0,
                    "callback": 0,
                    "badNumber": 0
                },
                {
                    "id": 2054,
                    "name": "Recent STB, Lapsed Donor",
                    "callAttempts": 204,
                    "leadsWithAttempts": 17,
                    "rules": [
                        {
                            "id": 15,
                            "uuid": "759aeada-31c3-409a-8945-9b5e2493ac05",
                            "name": "CTG Anytime",
                            "callAttempts": 204
                        }
                    ],
                    "leads": 17,
                    "exhausted": 0,
                    "viable": 17,
                    "dontContactUntil": 0,
                    "callback": 0,
                    "badNumber": 0
                },
                {
                    "id": 2055,
                    "name": "Lapsed Sub, Recent Donor",
                    "callAttempts": 144,
                    "leadsWithAttempts": 12,
                    "rules": [
                        {
                            "id": 15,
                            "uuid": "6aea936f-5851-48ed-a226-56e5f0cafeb6",
                            "name": "CTG Anytime",
                            "callAttempts": 144
                        }
                    ],
                    "leads": 12,
                    "exhausted": 0,
                    "viable": 7,
                    "dontContactUntil": 5,
                    "callback": 0,
                    "badNumber": 0
                },
                {
                    "id": 2056,
                    "name": "Recent STB, Recent Donor",
                    "callAttempts": 732,
                    "leadsWithAttempts": 61,
                    "rules": [
                        {
                            "id": 15,
                            "uuid": "904c4c57-bcb5-48e5-890e-c8f571736fab",
                            "name": "CTG Anytime",
                            "callAttempts": 732
                        }
                    ],
                    "leads": 61,
                    "exhausted": 0,
                    "viable": 42,
                    "dontContactUntil": 19,
                    "callback": 0,
                    "badNumber": 0
                },
                {
                    "id": 2057,
                    "name": "Mattress STB",
                    "callAttempts": 144,
                    "leadsWithAttempts": 12,
                    "rules": [
                        {
                            "id": 15,
                            "uuid": "0d937660-bf87-4898-b690-1548d5ddd9ad",
                            "name": "CTG Anytime",
                            "callAttempts": 144
                        }
                    ],
                    "leads": 12,
                    "exhausted": 0,
                    "viable": 12,
                    "dontContactUntil": 0,
                    "callback": 0,
                    "badNumber": 0
                },
                {
                    "id": 2062,
                    "name": "LYBUNT STBs 3+",
                    "callAttempts": 48,
                    "leadsWithAttempts": 4,
                    "rules": [
                        {
                            "id": 15,
                            "uuid": "2ccd834b-3d40-4f03-8790-98e01db7d3a4",
                            "name": "CTG Anytime",
                            "callAttempts": 48
                        }
                    ],
                    "leads": 4,
                    "exhausted": 0,
                    "viable": 1,
                    "dontContactUntil": 3,
                    "callback": 0,
                    "badNumber": 0
                },
                {
                    "id": 2086,
                    "name": "SUB ACQ Lapsed AHM 2023 Season Subs",
                    "callAttempts": 444,
                    "leadsWithAttempts": 37,
                    "rules": [
                        {
                            "id": 15,
                            "uuid": "2ccd834b-3d40-4f03-8790-98e01db7d3a4",
                            "name": "CTG Anytime",
                            "callAttempts": 444
                        }
                    ],
                    "leads": 37,
                    "exhausted": 0,
                    "viable": 35,
                    "dontContactUntil": 2,
                    "callback": 0,
                    "badNumber": 0
                },
                {
                    "id": 2088,
                    "name": "SUB ACQ Lapsed AHM 2024 Season Subs",
                    "callAttempts": 960,
                    "leadsWithAttempts": 80,
                    "rules": [
                        {
                            "id": 15,
                            "uuid": "2ccd834b-3d40-4f03-8790-98e01db7d3a4",
                            "name": "CTG Anytime",
                            "callAttempts": 960
                        }
                    ],
                    "leads": 82,
                    "exhausted": 2,
                    "viable": 76,
                    "dontContactUntil": 4,
                    "callback": 1,
                    "badNumber": 0
                },
                {
                    "id": 2089,
                    "name": "SUB ACQ Lapsed MTF 2019-21 Season Subs",
                    "callAttempts": 504,
                    "leadsWithAttempts": 42,
                    "rules": [
                        {
                            "id": 15,
                            "uuid": "2ccd834b-3d40-4f03-8790-98e01db7d3a4",
                            "name": "CTG Anytime",
                            "callAttempts": 504
                        }
                    ],
                    "leads": 42,
                    "exhausted": 0,
                    "viable": 30,
                    "dontContactUntil": 12,
                    "callback": 0,
                    "badNumber": 0
                },
                {
                    "id": 2090,
                    "name": "SUB ACQ Lapsed AHM 2019-22 Season Subs",
                    "callAttempts": 1200,
                    "leadsWithAttempts": 100,
                    "rules": [
                        {
                            "id": 15,
                            "uuid": "09f1557e-0bb2-434f-aac3-dcb92461c540",
                            "name": "CTG Anytime",
                            "callAttempts": 1200
                        }
                    ],
                    "leads": 101,
                    "exhausted": 1,
                    "viable": 78,
                    "dontContactUntil": 23,
                    "callback": 0,
                    "badNumber": 0
                },
                {
                    "id": 2100,
                    "name": "SUB ACQ Ancmt EM Click Recent",
                    "callAttempts": 12,
                    "leadsWithAttempts": 1,
                    "rules": [
                        {
                            "id": 15,
                            "uuid": "904c4c57-bcb5-48e5-890e-c8f571736fab",
                            "name": "CTG Anytime",
                            "callAttempts": 12
                        }
                    ],
                    "leads": 1,
                    "exhausted": 0,
                    "viable": 1,
                    "dontContactUntil": 0,
                    "callback": 0,
                    "badNumber": 0
                },
                {
                    "id": 2102,
                    "name": "SUB ACQ 44 STBs",
                    "callAttempts": 84,
                    "leadsWithAttempts": 7,
                    "rules": [
                        {
                            "id": 15,
                            "uuid": "2ccd834b-3d40-4f03-8790-98e01db7d3a4",
                            "name": "CTG Anytime",
                            "callAttempts": 84
                        }
                    ],
                    "leads": 7,
                    "exhausted": 0,
                    "viable": 7,
                    "dontContactUntil": 0,
                    "callback": 0,
                    "badNumber": 0
                },
                {
                    "id": 2103,
                    "name": "SUB ACQ MTF24 STBs",
                    "callAttempts": 1176,
                    "leadsWithAttempts": 98,
                    "rules": [
                        {
                            "id": 15,
                            "uuid": "2ccd834b-3d40-4f03-8790-98e01db7d3a4",
                            "name": "CTG Anytime",
                            "callAttempts": 1176
                        }
                    ],
                    "leads": 101,
                    "exhausted": 3,
                    "viable": 65,
                    "dontContactUntil": 36,
                    "callback": 0,
                    "badNumber": 0
                },
                {
                    "id": 2104,
                    "name": "SUB ACQ KDT24 STBs",
                    "callAttempts": 396,
                    "leadsWithAttempts": 33,
                    "rules": [
                        {
                            "id": 15,
                            "uuid": "2ccd834b-3d40-4f03-8790-98e01db7d3a4",
                            "name": "CTG Anytime",
                            "callAttempts": 396
                        }
                    ],
                    "leads": 34,
                    "exhausted": 1,
                    "viable": 20,
                    "dontContactUntil": 14,
                    "callback": 0,
                    "badNumber": 0
                },
                {
                    "id": 2105,
                    "name": "SUB ACQ AHM24 STBs",
                    "callAttempts": 4944,
                    "leadsWithAttempts": 412,
                    "rules": [
                        {
                            "id": 15,
                            "uuid": "2ccd834b-3d40-4f03-8790-98e01db7d3a4",
                            "name": "CTG Anytime",
                            "callAttempts": 4944
                        }
                    ],
                    "leads": 421,
                    "exhausted": 9,
                    "viable": 277,
                    "dontContactUntil": 143,
                    "callback": 0,
                    "badNumber": 0
                },
                {
                    "id": 2114,
                    "name": "SUB ACQ LOPI STBs",
                    "callAttempts": 360,
                    "leadsWithAttempts": 30,
                    "rules": [
                        {
                            "id": 15,
                            "uuid": "09f1557e-0bb2-434f-aac3-dcb92461c540",
                            "name": "CTG Anytime",
                            "callAttempts": 360
                        }
                    ],
                    "leads": 32,
                    "exhausted": 2,
                    "viable": 24,
                    "dontContactUntil": 7,
                    "callback": 0,
                    "badNumber": 0
                },
                {
                    "id": 2115,
                    "name": "SUB ACQ 19-20 STBs with 3+",
                    "callAttempts": 156,
                    "leadsWithAttempts": 13,
                    "rules": [
                        {
                            "id": 15,
                            "uuid": "61be7b8b-d595-4ed2-b5f3-856eee64cf90",
                            "name": "CTG Anytime",
                            "callAttempts": 156
                        }
                    ],
                    "leads": 13,
                    "exhausted": 0,
                    "viable": 10,
                    "dontContactUntil": 3,
                    "callback": 0,
                    "badNumber": 0
                },
                {
                    "id": 2117,
                    "name": "SUB ACQ 2yrs in a row",
                    "callAttempts": 372,
                    "leadsWithAttempts": 31,
                    "rules": [
                        {
                            "id": 15,
                            "uuid": "61be7b8b-d595-4ed2-b5f3-856eee64cf90",
                            "name": "CTG Anytime",
                            "callAttempts": 372
                        }
                    ],
                    "leads": 32,
                    "exhausted": 1,
                    "viable": 22,
                    "dontContactUntil": 10,
                    "callback": 0,
                    "badNumber": 0
                },
                {
                    "id": 2119,
                    "name": "SUB ACQ CATK STBs",
                    "callAttempts": 144,
                    "leadsWithAttempts": 12,
                    "rules": [
                        {
                            "id": 15,
                            "uuid": "759aeada-31c3-409a-8945-9b5e2493ac05",
                            "name": "CTG Anytime",
                            "callAttempts": 144
                        }
                    ],
                    "leads": 12,
                    "exhausted": 0,
                    "viable": 8,
                    "dontContactUntil": 4,
                    "callback": 0,
                    "badNumber": 0
                },
                {
                    "id": 2120,
                    "name": "SUB ACQ DUEL STBs",
                    "callAttempts": 96,
                    "leadsWithAttempts": 8,
                    "rules": [
                        {
                            "id": 15,
                            "uuid": "759aeada-31c3-409a-8945-9b5e2493ac05",
                            "name": "CTG Anytime",
                            "callAttempts": 96
                        }
                    ],
                    "leads": 8,
                    "exhausted": 0,
                    "viable": 6,
                    "dontContactUntil": 2,
                    "callback": 0,
                    "badNumber": 0
                },
                {
                    "id": 2122,
                    "name": "SUB ACQ FAKEIT STBs",
                    "callAttempts": 132,
                    "leadsWithAttempts": 11,
                    "rules": [
                        {
                            "id": 15,
                            "uuid": "2ccd834b-3d40-4f03-8790-98e01db7d3a4",
                            "name": "CTG Anytime",
                            "callAttempts": 132
                        }
                    ],
                    "leads": 11,
                    "exhausted": 0,
                    "viable": 9,
                    "dontContactUntil": 2,
                    "callback": 0,
                    "badNumber": 0
                },
                {
                    "id": 2123,
                    "name": "SUB ACQ HAM25 STBs",
                    "callAttempts": 12,
                    "leadsWithAttempts": 1,
                    "rules": [
                        {
                            "id": 15,
                            "uuid": "904c4c57-bcb5-48e5-890e-c8f571736fab",
                            "name": "CTG Anytime",
                            "callAttempts": 12
                        }
                    ],
                    "leads": 1,
                    "exhausted": 0,
                    "viable": 1,
                    "dontContactUntil": 0,
                    "callback": 0,
                    "badNumber": 0
                },
                {
                    "id": 2124,
                    "name": "SUB ACQ IDIOT STBs",
                    "callAttempts": 2424,
                    "leadsWithAttempts": 202,
                    "rules": [
                        {
                            "id": 15,
                            "uuid": "759aeada-31c3-409a-8945-9b5e2493ac05",
                            "name": "CTG Anytime",
                            "callAttempts": 2424
                        }
                    ],
                    "leads": 205,
                    "exhausted": 3,
                    "viable": 193,
                    "dontContactUntil": 10,
                    "callback": 0,
                    "badNumber": 0
                },
                {
                    "id": 2126,
                    "name": "SUB ACQ OLDF STBs",
                    "callAttempts": 708,
                    "leadsWithAttempts": 59,
                    "rules": [
                        {
                            "id": 15,
                            "uuid": "0d937660-bf87-4898-b690-1548d5ddd9ad",
                            "name": "CTG Anytime",
                            "callAttempts": 708
                        }
                    ],
                    "leads": 61,
                    "exhausted": 2,
                    "viable": 44,
                    "dontContactUntil": 17,
                    "callback": 0,
                    "badNumber": 0
                },
                {
                    "id": 2127,
                    "name": "SUB ACQ MATT STBs",
                    "callAttempts": 1812,
                    "leadsWithAttempts": 151,
                    "rules": [
                        {
                            "id": 15,
                            "uuid": "0d937660-bf87-4898-b690-1548d5ddd9ad",
                            "name": "CTG Anytime",
                            "callAttempts": 1812
                        }
                    ],
                    "leads": 152,
                    "exhausted": 1,
                    "viable": 137,
                    "dontContactUntil": 15,
                    "callback": 0,
                    "badNumber": 0
                },
                {
                    "id": 2131,
                    "name": "SUB RNW 25 Subs",
                    "callAttempts": 24,
                    "leadsWithAttempts": 2,
                    "rules": [
                        {
                            "id": 15,
                            "uuid": "e24e2ba8-d423-43b6-aa18-8b68fa95b1e9",
                            "name": "CTG Anytime",
                            "callAttempts": 24
                        }
                    ],
                    "leads": 2,
                    "exhausted": 0,
                    "viable": 2,
                    "dontContactUntil": 0,
                    "callback": 0,
                    "badNumber": 0
                },
                {
                    "id": 2093,
                    "name": "SUB ACQ Callback from 24/25 Campaign",
                    "exhausted": 4,
                    "leads": 4,
                    "viable": 0,
                    "dontContactUntil": 4,
                    "callback": 0,
                    "badNumber": 0,
                    "rules": []
                },
                {
                    "id": 2132,
                    "name": "SUB RNW AR Failed",
                    "exhausted": 1,
                    "leads": 1,
                    "viable": 0,
                    "dontContactUntil": 1,
                    "callback": 0,
                    "badNumber": 0,
                    "rules": []
                }
            ],
            "leads": 1512,
            "exhausted": 30,
            "viable": 1170,
            "dontContactUntil": 336,
            "callback": 1,
            "badNumber": 0
        },
        {
            "id": 1189,
            "name": "Thank you",
            "leadsWithAttempts": 27,
            "callAttempts": 78,
            "skills": [
                {
                    "id": 2036,
                    "name": "Prospect Lead",
                    "callAttempts": 3,
                    "leadsWithAttempts": 1,
                    "rules": [
                        {
                            "id": "02f8f749-feb4-4d07-aba8-5ca78863018d",
                            "name": "CTG Anytime",
                            "callAttempts": 3,
                            "leadsWithAttempts": 1
                        }
                    ],
                    "leads": 1,
                    "exhausted": 0,
                    "viable": 1,
                    "dontContactUntil": 0,
                    "callback": 0,
                    "badNumber": 0
                },
                {
                    "id": 2054,
                    "name": "Recent STB, Lapsed Donor",
                    "callAttempts": 3,
                    "leadsWithAttempts": 1,
                    "rules": [
                        {
                            "id": 15,
                            "uuid": "d1aa4411-b60e-4f87-bfff-ceafe039aa7d",
                            "name": "CTG Anytime",
                            "callAttempts": 3
                        }
                    ],
                    "leads": 1,
                    "exhausted": 0,
                    "viable": 1,
                    "dontContactUntil": 0,
                    "callback": 0,
                    "badNumber": 0
                },
                {
                    "id": 2055,
                    "name": "Lapsed Sub, Recent Donor",
                    "callAttempts": 3,
                    "leadsWithAttempts": 1,
                    "rules": [
                        {
                            "id": 15,
                            "uuid": "5e0dbf4c-6dba-48bd-9af3-d6961f5e33d8",
                            "name": "CTG Anytime",
                            "callAttempts": 3
                        }
                    ],
                    "leads": 1,
                    "exhausted": 0,
                    "viable": 1,
                    "dontContactUntil": 0,
                    "callback": 0,
                    "badNumber": 0
                },
                {
                    "id": 2056,
                    "name": "Recent STB, Recent Donor",
                    "callAttempts": 20,
                    "leadsWithAttempts": 7,
                    "rules": [
                        {
                            "id": 15,
                            "uuid": "39974771-b928-4e4e-83dc-43c9916bf404",
                            "name": "CTG Anytime",
                            "callAttempts": 20
                        }
                    ],
                    "leads": 8,
                    "exhausted": 1,
                    "viable": 7,
                    "dontContactUntil": 0,
                    "callback": 0,
                    "badNumber": 0
                },
                {
                    "id": 2086,
                    "name": "SUB ACQ Lapsed AHM 2023 Season Subs",
                    "callAttempts": 3,
                    "leadsWithAttempts": 1,
                    "rules": [
                        {
                            "id": 15,
                            "uuid": "e646d9ad-617c-43f4-9a16-562b7371a292",
                            "name": "CTG Anytime",
                            "callAttempts": 3
                        }
                    ],
                    "leads": 2,
                    "exhausted": 1,
                    "viable": 1,
                    "dontContactUntil": 0,
                    "callback": 1,
                    "badNumber": 0
                },
                {
                    "id": 2088,
                    "name": "SUB ACQ Lapsed AHM 2024 Season Subs",
                    "callAttempts": 3,
                    "leadsWithAttempts": 1,
                    "rules": [
                        {
                            "id": 15,
                            "uuid": "a11573ac-0464-455b-9dcc-949e95ef3d15",
                            "name": "CTG Anytime",
                            "callAttempts": 3
                        }
                    ],
                    "leads": 5,
                    "exhausted": 4,
                    "viable": 1,
                    "dontContactUntil": 3,
                    "callback": 0,
                    "badNumber": 0
                },
                {
                    "id": 2090,
                    "name": "SUB ACQ Lapsed AHM 2019-22 Season Subs",
                    "callAttempts": 15,
                    "leadsWithAttempts": 5,
                    "rules": [
                        {
                            "id": 15,
                            "uuid": "39974771-b928-4e4e-83dc-43c9916bf404",
                            "name": "CTG Anytime",
                            "callAttempts": 15
                        }
                    ],
                    "leads": 6,
                    "exhausted": 1,
                    "viable": 2,
                    "dontContactUntil": 2,
                    "callback": 2,
                    "badNumber": 0
                },
                {
                    "id": 2105,
                    "name": "SUB ACQ AHM24 STBs",
                    "callAttempts": 12,
                    "leadsWithAttempts": 4,
                    "rules": [
                        {
                            "id": 15,
                            "uuid": "39974771-b928-4e4e-83dc-43c9916bf404",
                            "name": "CTG Anytime",
                            "callAttempts": 12
                        }
                    ],
                    "leads": 5,
                    "exhausted": 1,
                    "viable": 2,
                    "dontContactUntil": 2,
                    "callback": 1,
                    "badNumber": 0
                },
                {
                    "id": 2114,
                    "name": "SUB ACQ LOPI STBs",
                    "callAttempts": 10,
                    "leadsWithAttempts": 4,
                    "rules": [
                        {
                            "id": 15,
                            "uuid": "39974771-b928-4e4e-83dc-43c9916bf404",
                            "name": "CTG Anytime",
                            "callAttempts": 10
                        }
                    ],
                    "leads": 6,
                    "exhausted": 2,
                    "viable": 1,
                    "dontContactUntil": 2,
                    "callback": 2,
                    "badNumber": 0
                },
                {
                    "id": 2134,
                    "name": "SUB RNW 1st Yr Sub",
                    "callAttempts": 6,
                    "leadsWithAttempts": 2,
                    "rules": [
                        {
                            "id": 15,
                            "uuid": "39974771-b928-4e4e-83dc-43c9916bf404",
                            "name": "CTG Anytime",
                            "callAttempts": 6
                        }
                    ],
                    "leads": 4,
                    "exhausted": 2,
                    "viable": 1,
                    "dontContactUntil": 2,
                    "callback": 1,
                    "badNumber": 0
                },
                {
                    "id": 2053,
                    "name": "Lapsed Sub, Lapsed Donor",
                    "exhausted": 1,
                    "leads": 1,
                    "viable": 0,
                    "dontContactUntil": 0,
                    "callback": 0,
                    "badNumber": 0,
                    "rules": []
                },
                {
                    "id": 2111,
                    "name": "SUB RNW 3rd Yr",
                    "exhausted": 1,
                    "leads": 1,
                    "viable": 0,
                    "dontContactUntil": 1,
                    "callback": 0,
                    "badNumber": 0,
                    "rules": []
                },
                {
                    "id": 2112,
                    "name": "SUB RNW 2nd Yr",
                    "exhausted": 2,
                    "leads": 2,
                    "viable": 0,
                    "dontContactUntil": 1,
                    "callback": 0,
                    "badNumber": 0,
                    "rules": []
                },
                {
                    "id": 2131,
                    "name": "SUB RNW 25 Subs",
                    "exhausted": 2,
                    "leads": 2,
                    "viable": 0,
                    "dontContactUntil": 0,
                    "callback": 0,
                    "badNumber": 0,
                    "rules": []
                },
                {
                    "id": 2132,
                    "name": "SUB RNW AR Failed",
                    "exhausted": 1,
                    "leads": 1,
                    "viable": 0,
                    "dontContactUntil": 0,
                    "callback": 0,
                    "badNumber": 0,
                    "rules": []
                },
                {
                    "id": null,
                    "name": "No lead type found",
                    "exhausted": 0,
                    "leads": 0,
                    "viable": 0,
                    "dontContactUntil": 0,
                    "callback": 0,
                    "badNumber": 0,
                    "rules": [],
                    "suppressions": 3
                }
            ],
            "leads": 46,
            "exhausted": 19,
            "viable": 18,
            "dontContactUntil": 13,
            "callback": 7,
            "badNumber": 0,
            "suppressions": 3
        },
        {
            "id": 1190,
            "name": "2nd Appeal",
            "leadsWithAttempts": 2,
            "callAttempts": 25,
            "skills": [
                {
                    "id": 2090,
                    "name": "SUB ACQ Lapsed AHM 2019-22 Season Subs",
                    "callAttempts": 12,
                    "leadsWithAttempts": 1,
                    "rules": [
                        {
                            "id": "18377c4e-df57-417e-9c01-3c9b9442a481",
                            "name": "CTG Anytime",
                            "callAttempts": 12,
                            "leadsWithAttempts": 1
                        }
                    ],
                    "leads": 1,
                    "exhausted": 0,
                    "viable": 0,
                    "dontContactUntil": 1,
                    "callback": 0,
                    "badNumber": 0
                },
                {
                    "id": 2137,
                    "name": "SUB RNW 1st Yr MTF",
                    "callAttempts": 13,
                    "leadsWithAttempts": 1,
                    "rules": [
                        {
                            "id": 15,
                            "uuid": "d9c2434a-fe62-4547-8c12-2d6ebcf36a24",
                            "name": "CTG Anytime",
                            "callAttempts": 12
                        }
                    ],
                    "leads": 1,
                    "exhausted": 0,
                    "viable": 0,
                    "dontContactUntil": 0,
                    "callback": 1,
                    "badNumber": 0
                }
            ],
            "leads": 2,
            "exhausted": 0,
            "viable": 0,
            "dontContactUntil": 1,
            "callback": 1,
            "badNumber": 0
        }
    ],
    "suppressedNoStage": 1023,
    "suppressedTotal": 1043
}