var APP_SETTINGS = require('./config/constants')
var Sequelize = require('sequelize'),
    db = new Sequelize(APP_SETTINGS.DB.schema, APP_SETTINGS.DB.user, APP_SETTINGS.DB.pass, APP_SETTINGS.DB.config)
var Models = require('./database/schema')(db, Sequelize)

// Test configuration
const TEST_STAGE_ID = 1  // Update this to a valid campaign stage ID in your database
const TEST_AGENT_ID_1 = 1 // Update this to a valid agent ID
const TEST_AGENT_ID_2 = 2 // Update this to a valid agent ID

console.log('=== Lead Locking Mechanism Test ===')
console.log('Testing concurrent lead assignment to verify the fix...')

// Test concurrent lead assignment
testConcurrentLeadAssignment()

function testConcurrentLeadAssignment() {
    console.log('\nStarting concurrent lead assignment test...')
    
    // Simulate two agents trying to get leads simultaneously
    const agent1Promise = simulateAgentGettingLead(TEST_AGENT_ID_1, 'Agent1')
    const agent2Promise = simulateAgentGettingLead(TEST_AGENT_ID_2, 'Agent2')
    
    Promise.all([agent1Promise, agent2Promise])
        .then(results => {
            const [result1, result2] = results
            
            console.log('\n=== TEST RESULTS ===')
            console.log('Agent 1 result:', result1 ? `Lead ${result1.leadId} assigned` : 'No lead assigned')
            console.log('Agent 2 result:', result2 ? `Lead ${result2.leadId} assigned` : 'No lead assigned')
            
            if (result1 && result2 && result1.leadId === result2.leadId) {
                console.log('❌ FAILURE: Both agents got the same lead! Lead ID:', result1.leadId)
                console.log('The locking mechanism is NOT working properly.')
                process.exit(1)
            } else if (result1 || result2) {
                console.log('✅ SUCCESS: No duplicate lead assignment detected')
                console.log('The locking mechanism is working correctly.')
                
                if (result1 && result2) {
                    console.log(`Both agents got different leads: ${result1.leadId} vs ${result2.leadId}`)
                } else {
                    console.log('Only one agent got a lead, which is expected behavior.')
                }
                process.exit(0)
            } else {
                console.log('⚠️  WARNING: No leads were assigned to either agent')
                console.log('This could mean no leads are available or there\'s a configuration issue.')
                process.exit(0)
            }
        })
        .catch(err => {
            console.error('Test failed with error:', err.message)
            process.exit(1)
        })
}

function simulateAgentGettingLead(agentId, agentName) {
    console.log(`[${agentName}] Starting lead selection for agent ${agentId}...`)
    
    var now = new Date()
    var campaignStage = { id: TEST_STAGE_ID }
    var session = { agentId: agentId }
    
    // Simplified lead selection query (matching the main logic)
    var callAttemptWhereQuery = {
        dontContactUntil: {
            $or: {
                $eq: null,
                $lt: now
            }
        },
        $or: [{
            phone_home: { $and: { $not: null, $ne: '' } }
        }, {
            phone_mobile: { $and: { $not: null, $ne: '' } }
        }, {
            phone_work: { $and: { $not: null, $ne: '' } }
        }, {
            phone_workmobile: { $and: { $not: null, $ne: '' } }
        }]
    }
    
    return db.transaction({
        autocommit: false,
        isolationLevel: db.Transaction.ISOLATION_LEVELS.READ_COMMITTED
    }, function (t) {
        // Use the new locking mechanism - lock Lead directly
        return Models.Lead.findOne({
            where: callAttemptWhereQuery,
            include: [{
                model: Models.CallAttempt,
                where: {
                    campaignstageId: campaignStage.id
                },
                required: true
            }],
            order: [
                [Models.CallAttempt, 'lastDispositionDate'],
                [Models.CallAttempt, 'randomSelector']
            ],
            transaction: t,
            lock: t.LOCK.UPDATE
        }).then(function (lead) {
            if (lead) {
                console.log(`[${agentName}] Selected lead ${lead.id}`)
                
                // Check if lead is already assigned
                if (lead.lastAgent && lead.lastAgent !== session.agentId && 
                    lead.dontContactUntil && lead.dontContactUntil > now) {
                    console.log(`[${agentName}] Lead ${lead.id} already assigned to agent ${lead.lastAgent}, skipping`)
                    return Promise.resolve()
                }
                
                return lead.updateAttributes({
                    dontContactUntil: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours from now
                    lastAgent: session.agentId
                }, {
                    transaction: t
                }).then(() => {
                    console.log(`[${agentName}] Successfully assigned lead ${lead.id} to agent ${session.agentId}`)
                    
                    // Return the call attempt for consistency
                    return Models.CallAttempt.findOne({
                        where: {
                            leadId: lead.id,
                            campaignstageId: campaignStage.id
                        },
                        include: [{ model: Models.Lead }],
                        transaction: t
                    })
                }).catch(Promise.reject)
            } else {
                console.log(`[${agentName}] No available leads found`)
                return Promise.resolve()
            }
        }).catch(Promise.reject)
    }).catch(err => {
        console.error(`[${agentName}] Error in lead assignment:`, err.message)
        return Promise.resolve() // Don't fail the test, just return no result
    })
}
