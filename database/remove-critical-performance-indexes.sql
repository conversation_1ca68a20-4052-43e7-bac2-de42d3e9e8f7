-- Remove Critical Performance Indexes for callattemptanalysisv2 Endpoint
-- 
-- This script removes the indexes created by critical-performance-indexes.sql
-- Use this if you need to rollback the index changes due to performance issues
-- or if the indexes are causing problems with write operations.
--
-- IMPORTANT:
-- - Run during maintenance window 
-- - This will restore original query performance (5-10 minute load times)
-- - Write performance will return to original speeds
-- - Will free up ~2-4 GB of disk space
-- - Test on staging environment first
--
-- Estimated execution time: 5-15 minutes

-- Disable foreign key checks during index removal for safety
SET FOREIGN_KEY_CHECKS = 0;

-- ============================================================================
-- VERIFY INDEXES EXIST BEFORE REMOVAL
-- ============================================================================

-- Check which indexes currently exist
SELECT 
    TABLE_NAME,
    INDEX_NAME,
    COLUMN_NAME,
    SEQ_IN_INDEX,
    'EXISTS' as STATUS
FROM INFORMATION_SCHEMA.STATISTICS 
WHERE TABLE_SCHEMA = DATABASE()
    AND INDEX_NAME IN (
        'idx_callattempts_campaign_lead',
        'idx_campaignleads_campaign_stage_lead', 
        'idx_leads_tf_skills',
        'idx_leads_tm_skills',
        'idx_callbacks_lead_active',
        'idx_suppressions_campaign_lead_active',
        'idx_callattempts_lead_campaign_exists',
        'idx_callresults_campaign_stage_lead',
        'idx_leads_agent_portfolio'
    )
ORDER BY TABLE_NAME, INDEX_NAME, SEQ_IN_INDEX;

-- ============================================================================
-- REMOVE INDEXES (In reverse order of creation for safety)
-- ============================================================================

-- Remove agent portfolio filtering index
DROP INDEX IF EXISTS idx_leads_agent_portfolio ON leads;

-- Remove call results optimization index
DROP INDEX IF EXISTS idx_callresults_campaign_stage_lead ON callresults;

-- Remove call attempts EXISTS check index
DROP INDEX IF EXISTS idx_callattempts_lead_campaign_exists ON callattempts;

-- Remove suppression EXISTS check index  
DROP INDEX IF EXISTS idx_suppressions_campaign_lead_active ON suppressions;

-- Remove callback EXISTS check index
DROP INDEX IF EXISTS idx_callbacks_lead_active ON callbacks;

-- Remove telemarketing skill grouping index
DROP INDEX IF EXISTS idx_leads_tm_skills ON leads;

-- Remove telefunding skill grouping index
DROP INDEX IF EXISTS idx_leads_tf_skills ON leads;

-- Remove campaign leads stage filtering index
DROP INDEX IF EXISTS idx_campaignleads_campaign_stage_lead ON campaignleads;

-- Remove core call attempts index
DROP INDEX IF EXISTS idx_callattempts_campaign_lead ON callattempts;

-- Re-enable foreign key checks
SET FOREIGN_KEY_CHECKS = 1;

-- ============================================================================
-- VERIFICATION QUERIES
-- ============================================================================

-- Verify indexes were removed successfully
-- This should return no rows if all indexes were removed
SELECT 
    TABLE_NAME,
    INDEX_NAME,
    COLUMN_NAME,
    SEQ_IN_INDEX,
    'STILL EXISTS - REMOVAL FAILED' as STATUS
FROM INFORMATION_SCHEMA.STATISTICS 
WHERE TABLE_SCHEMA = DATABASE()
    AND INDEX_NAME IN (
        'idx_callattempts_campaign_lead',
        'idx_campaignleads_campaign_stage_lead', 
        'idx_leads_tf_skills',
        'idx_leads_tm_skills',
        'idx_callbacks_lead_active',
        'idx_suppressions_campaign_lead_active',
        'idx_callattempts_lead_campaign_exists',
        'idx_callresults_campaign_stage_lead',
        'idx_leads_agent_portfolio'
    )
ORDER BY TABLE_NAME, INDEX_NAME, SEQ_IN_INDEX;

-- Check remaining indexes on affected tables
SELECT 
    TABLE_NAME,
    INDEX_NAME,
    COLUMN_NAME,
    SEQ_IN_INDEX
FROM INFORMATION_SCHEMA.STATISTICS 
WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME IN ('callattempts', 'campaignleads', 'leads', 'callbacks', 'suppressions', 'callresults')
    AND INDEX_NAME != 'PRIMARY'
ORDER BY TABLE_NAME, INDEX_NAME, SEQ_IN_INDEX;

-- Check table sizes after index removal
SELECT 
    TABLE_NAME,
    ROUND(((DATA_LENGTH + INDEX_LENGTH) / 1024 / 1024), 2) AS 'Total Size (MB)',
    ROUND((DATA_LENGTH / 1024 / 1024), 2) AS 'Data Size (MB)',
    ROUND((INDEX_LENGTH / 1024 / 1024), 2) AS 'Index Size (MB)'
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME IN ('callattempts', 'campaignleads', 'leads', 'callbacks', 'suppressions', 'callresults')
ORDER BY (DATA_LENGTH + INDEX_LENGTH) DESC;

-- ============================================================================
-- POST-REMOVAL RECOMMENDATIONS
-- ============================================================================

-- After removing indexes, consider:
-- 1. Run ANALYZE TABLE to update statistics
-- 2. Monitor callattemptanalysisv2 endpoint performance (should return to 5-10 min)
-- 3. Check write operation performance (should improve)
-- 4. Verify disk space was freed up
-- 5. Update any monitoring/alerting thresholds

-- Optional: Update table statistics after index removal
-- ANALYZE TABLE callattempts;
-- ANALYZE TABLE campaignleads; 
-- ANALYZE TABLE leads;
-- ANALYZE TABLE callbacks;
-- ANALYZE TABLE suppressions;
-- ANALYZE TABLE callresults;

-- ============================================================================
-- NOTES
-- ============================================================================

-- Performance Impact After Removal:
-- - callattemptanalysisv2 endpoint: Returns to 5-10 minute load times
-- - Write operations: Return to original speeds (15-25% faster INSERTs/UPDATEs)
-- - Disk space: Frees up ~2-4 GB
-- - Memory usage: Reduces buffer pool pressure by ~1-2 GB

-- If you need to re-add indexes later:
-- - Run critical-performance-indexes.sql again
-- - All indexes can be safely re-created
-- - Performance improvements will return

-- Troubleshooting:
-- - If DROP INDEX fails, check for active queries using the indexes
-- - Use SHOW PROCESSLIST to see running queries
-- - Wait for long-running queries to complete before retrying
