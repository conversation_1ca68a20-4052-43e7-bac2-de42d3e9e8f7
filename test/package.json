{"name": "callanalysisv2-tests", "version": "1.0.0", "description": "Test suite for CallAnalysisV2 optimization validation", "scripts": {"test": "npm run test:unit && npm run test:integration && npm run test:validation", "test:unit": "mocha unit/processStageViewV2Data.test.js --reporter spec", "test:integration": "mocha integration/callanalysisv2.integration.test.js --reporter spec --timeout 10000", "test:validation": "node runCallAnalysisV2Tests.js", "test:performance": "node runCallAnalysisV2Tests.js | grep -E '(Duration|Time per lead|Performance Summary)'", "test:watch": "mocha unit/**/*.test.js integration/**/*.test.js --watch --reporter spec"}, "devDependencies": {"chai": "^3.5.0", "mocha": "^3.5.3", "supertest": "^2.0.1", "underscore": "^1.8.3"}, "keywords": ["testing", "performance", "optimization", "database", "mysql"], "author": "CallAnalysisV2 Optimization Team", "license": "ISC"}