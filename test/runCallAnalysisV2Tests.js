#!/usr/bin/env node

/**
 * Comprehensive test runner for CallAnalysisV2 optimization validation
 * 
 * This script runs all tests and generates a detailed report on:
 * - Data consistency across the 3-query optimization
 * - Performance improvements
 * - Dial count bucket accuracy
 * - Edge case handling
 * 
 * Usage: node test/runCallAnalysisV2Tests.js
 */

const { DataValidator, TestDataGenerator, PerformanceBenchmark } = require('./utils/dataValidation')

console.log('🚀 CallAnalysisV2 Optimization Test Suite')
console.log('=========================================\n')

// Test configuration
const TEST_CONFIGS = [
    { name: 'Small Dataset', leadCount: 100, description: 'Basic functionality test' },
    { name: 'Medium Dataset', leadCount: 1000, description: 'Performance validation' },
    { name: 'Large Dataset', leadCount: 10000, description: 'Scale testing (simulates 68K records)' },
    { name: 'Edge Cases', leadCount: 50, description: 'Boundary condition testing' }
]

/**
 * Simulate the 3-query approach
 */
function simulateThreeQueryApproach(campaignId, leadCount) {
    const benchmark = new PerformanceBenchmark(`3-Query Approach (${leadCount} leads)`)
    benchmark.start()

    // Generate test data
    const campaignLeads = TestDataGenerator.generateCampaignLeads(leadCount, campaignId)
    const leadIds = campaignLeads.map(cl => cl.leadid)
    const callResults = TestDataGenerator.generateCallResults(leadIds, campaignId, 0.7)
    const callAttempts = TestDataGenerator.generateCallAttempts(leadIds, campaignId)

    // Simulate Query 1: Call Attempts Analysis
    const query1Start = Date.now()
    const query1Results = simulateQuery1(callAttempts, campaignLeads, campaignId)
    const query1Time = Date.now() - query1Start

    // Simulate Query 2: Call Results Count
    const query2Start = Date.now()
    const query2Results = simulateQuery2(callResults, campaignId)
    const query2Time = Date.now() - query2Start

    // Simulate Query 3: Campaign Leads
    const query3Start = Date.now()
    const query3Results = simulateQuery3(campaignLeads, campaignId)
    const query3Time = Date.now() - query3Start

    // Simulate JavaScript Processing
    const jsStart = Date.now()
    const processedResults = simulateJavaScriptProcessing(query1Results, query2Results, query3Results)
    const jsTime = Date.now() - jsStart

    benchmark.end()
    benchmark.addMetric('query1Time', query1Time)
    benchmark.addMetric('query2Time', query2Time)
    benchmark.addMetric('query3Time', query3Time)
    benchmark.addMetric('jsProcessingTime', jsTime)
    benchmark.addMetric('leadCount', leadCount)
    benchmark.addMetric('callResultsCount', callResults.length)
    benchmark.addMetric('callAttemptsCount', callAttempts.length)

    return {
        query1Results,
        query2Results,
        query3Results,
        processedResults,
        benchmark: benchmark.getResults(),
        rawData: { campaignLeads, callResults, callAttempts }
    }
}

function simulateQuery1(callAttempts, campaignLeads, campaignId) {
    // Group call attempts by stage/skill combinations
    const groups = {}
    let totalAttempts = 0
    const totalLeads = new Set()

    callAttempts.filter(ca => ca.campaignid === campaignId).forEach(ca => {
        const cl = campaignLeads.find(cl => cl.leadid === ca.leadid)
        if (cl) {
            const key = `${cl.currentCampaignStageId}-${cl.tfSkillId}-${cl.tfSubskillid}`
            if (!groups[key]) {
                groups[key] = {
                    stageId: cl.currentCampaignStageId,
                    reportingGroupId: cl.tfSkillId,
                    skillId: cl.tfSubskillid,
                    attempts: 0,
                    leads: new Set()
                }
            }
            groups[key].attempts++
            groups[key].leads.add(ca.leadid)
            totalAttempts++
            totalLeads.add(ca.leadid)
        }
    })

    const results = Object.keys(groups).map(key => {
        const group = groups[key]
        return {
            stageId: group.stageId,
            reportingGroupId: group.reportingGroupId,
            skillId: group.skillId,
            attempts: group.attempts,
            leads: group.leads.size
        }
    })

    // Add total row (WITH ROLLUP simulation)
    results.unshift({
        stageId: null,
        reportingGroupId: null,
        skillId: null,
        attempts: totalAttempts,
        leads: totalLeads.size
    })

    return results
}

function simulateQuery2(callResults, campaignId) {
    const results = {}
    callResults.filter(cr => cr.campaignid === campaignId).forEach(cr => {
        if (!results[cr.leadid]) {
            results[cr.leadid] = { leadid: cr.leadid, calls_made: 0 }
        }
        results[cr.leadid].calls_made++
    })
    return Object.keys(results).map(key => results[key])
}

function simulateQuery3(campaignLeads, campaignId) {
    return campaignLeads
        .filter(cl => cl.campaignid === campaignId && cl.currentCampaignStageId)
        .map(cl => ({
            leadid: cl.leadid,
            stageId: cl.currentCampaignStageId,
            reportingGroupId: cl.tfSkillId,
            skillId: cl.tfSubskillid
        }))
}

function simulateJavaScriptProcessing(query1Results, query2Results, query3Results) {
    // Create lookup map (key optimization)
    const callResultsMap = {}
    query2Results.forEach(row => {
        callResultsMap[row.leadid] = row.calls_made || 0
    })

    // Calculate dial count buckets
    const dialCounts = { zero: 0, one: 0, twoToFour: 0, fiveToNineteen: 0, twentyPlus: 0 }
    
    query3Results.forEach(lead => {
        const callsMade = callResultsMap[lead.leadid] || 0
        
        if (callsMade === 0) dialCounts.zero++
        else if (callsMade === 1) dialCounts.one++
        else if (callsMade >= 2 && callsMade <= 4) dialCounts.twoToFour++
        else if (callsMade >= 5 && callsMade <= 19) dialCounts.fiveToNineteen++
        else if (callsMade >= 20) dialCounts.twentyPlus++
    })

    const totalRow = query1Results.find(r => r.stageId === null)
    return {
        totalDialAttemptBuckets: dialCounts,
        stages: query1Results.filter(r => r.stageId !== null),
        totalAttempts: totalRow ? totalRow.attempts : 0,
        totalLeads: query3Results.length
    }
}

/**
 * Run comprehensive validation tests
 */
function runValidationTests() {
    console.log('📊 Running Validation Tests...\n')
    
    const allResults = []
    
    TEST_CONFIGS.forEach(config => {
        console.log(`Testing: ${config.name} (${config.leadCount} leads)`)
        console.log(`Purpose: ${config.description}`)
        
        const campaignId = 1
        const testResult = simulateThreeQueryApproach(campaignId, config.leadCount)
        
        // Validate data consistency
        const validator = new DataValidator()
        validator.validateQueryTotals(
            testResult.query1Results, 
            testResult.query2Results, 
            testResult.query3Results, 
            config.name
        )
        
        validator.validateDialCountBuckets(
            testResult.processedResults.totalDialAttemptBuckets,
            testResult.query3Results.length,
            config.name
        )
        
        const validationReport = validator.generateReport()
        
        // Performance analysis
        const benchmark = testResult.benchmark
        const timePerLead = benchmark.duration / config.leadCount
        
        console.log(`  ✅ Duration: ${benchmark.duration}ms`)
        console.log(`  ⚡ Time per lead: ${timePerLead.toFixed(3)}ms`)
        console.log(`  📈 Query breakdown:`)
        console.log(`     - Query 1: ${benchmark.metrics.query1Time}ms`)
        console.log(`     - Query 2: ${benchmark.metrics.query2Time}ms`)
        console.log(`     - Query 3: ${benchmark.metrics.query3Time}ms`)
        console.log(`     - JS Processing: ${benchmark.metrics.jsProcessingTime}ms`)
        console.log(`  🎯 Validation: ${validationReport.isValid ? 'PASSED' : 'FAILED'}`)
        
        if (!validationReport.isValid) {
            console.log(`  ❌ Errors: ${validationReport.errors.join(', ')}`)
        }
        
        if (validationReport.warnings.length > 0) {
            console.log(`  ⚠️  Warnings: ${validationReport.warnings.join(', ')}`)
        }
        
        console.log(`  📊 Dial Count Distribution:`)
        const buckets = testResult.processedResults.totalDialAttemptBuckets
        console.log(`     - Zero calls: ${buckets.zero}`)
        console.log(`     - One call: ${buckets.one}`)
        console.log(`     - 2-4 calls: ${buckets.twoToFour}`)
        console.log(`     - 5-19 calls: ${buckets.fiveToNineteen}`)
        console.log(`     - 20+ calls: ${buckets.twentyPlus}`)
        
        console.log('')
        
        allResults.push({
            config,
            testResult,
            validationReport,
            timePerLead
        })
    })
    
    return allResults
}

/**
 * Generate final report
 */
function generateFinalReport(allResults) {
    console.log('📋 Final Test Report')
    console.log('===================\n')
    
    const totalTests = allResults.length
    const passedTests = allResults.filter(r => r.validationReport.isValid).length
    const failedTests = totalTests - passedTests
    
    console.log(`Total Tests: ${totalTests}`)
    console.log(`Passed: ${passedTests}`)
    console.log(`Failed: ${failedTests}`)
    console.log(`Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%\n`)
    
    // Performance summary
    console.log('Performance Summary:')
    allResults.forEach(result => {
        const config = result.config
        const timePerLead = result.timePerLead
        const status = timePerLead < 0.1 ? '🟢 EXCELLENT' : timePerLead < 0.5 ? '🟡 GOOD' : '🔴 NEEDS IMPROVEMENT'
        console.log(`  ${config.name}: ${timePerLead.toFixed(3)}ms per lead ${status}`)
    })
    
    if (failedTests === 0) {
        console.log('\n🎉 All tests passed!')
    } else {
        console.log('\n⚠️  Some tests failed. Please review the errors above.')
    }
}

// Run the test suite
if (require.main === module) {
    try {
        const results = runValidationTests()
        generateFinalReport(results)
    } catch (error) {
        console.error('❌ Test suite failed:', error)
        process.exit(1)
    }
}
