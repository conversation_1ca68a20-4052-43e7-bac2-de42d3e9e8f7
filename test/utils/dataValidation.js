/**
 * Data validation utilities for CallAnalysisV2 testing
 * Ensures data consistency and correctness across query results
 */

const _ = require('underscore')

class DataValidator {
    constructor() {
        this.errors = []
        this.warnings = []
    }

    /**
     * Validate that dial count buckets sum correctly
     */
    validateDialCountBuckets(buckets, expectedTotal, context = '') {
        const total = buckets.zero + buckets.one + buckets.twoToFour + 
                     buckets.fiveToNineteen + buckets.twentyPlus
        
        if (total !== expectedTotal) {
            this.errors.push(`${context}: Dial count buckets sum to ${total}, expected ${expectedTotal}`)
            return false
        }
        return true
    }

    /**
     * Validate that call results map correctly to campaign leads
     */
    validateCallResultsMapping(callResultsData, campaignLeadsData, context = '') {
        const callResultLeadIds = new Set(callResultsData.map(cr => cr.leadid))
        const campaignLeadIds = new Set(campaignLeadsData.map(cl => cl.leadid))
        
        // All leads with call results should be in campaign leads
        const orphanedCallResults = [...callResultLeadIds].filter(id => !campaignLeadIds.has(id))
        if (orphanedCallResults.length > 0) {
            this.errors.push(`${context}: Call results exist for leads not in campaign: ${orphanedCallResults.join(', ')}`)
            return false
        }
        
        return true
    }

    /**
     * Validate query totals consistency
     */
    validateQueryTotals(query1Results, query2Results, query3Results, context = '') {
        const totalRow = query1Results.find(r => r.stageId === null)
        if (!totalRow) {
            this.errors.push(`${context}: Query 1 missing total row (stageId = null)`)
            return false
        }

        // Total leads in query 1 should match campaign leads count
        if (totalRow.leads !== query3Results.length) {
            this.errors.push(`${context}: Query 1 total leads (${totalRow.leads}) != Query 3 leads count (${query3Results.length})`)
            return false
        }

        // All call result leads should exist in campaign leads
        return this.validateCallResultsMapping(query2Results, query3Results, context)
    }

    /**
     * Validate stage grouping consistency
     */
    validateStageGrouping(callAttemptsData, campaignLeadsData, context = '') {
        const detailRows = callAttemptsData.filter(r => r.stageId !== null)
        const stageGroups = _.groupBy(detailRows, 'stageId')
        const campaignStageGroups = _.groupBy(campaignLeadsData, 'stageId')
        
        // Check that all stages in call attempts exist in campaign leads
        Object.keys(stageGroups).forEach(stageId => {
            if (!campaignStageGroups[stageId]) {
                this.errors.push(`${context}: Stage ${stageId} in call attempts but not in campaign leads`)
            }
        })
        
        return this.errors.length === 0
    }

    /**
     * Validate dial count bucket calculations
     */
    validateDialCountCalculation(callResultsData, campaignLeadsData, expectedBuckets, context = '') {
        // Create lookup map
        const callResultsMap = {}
        callResultsData.forEach(row => {
            callResultsMap[row.leadid] = row.calls_made || 0
        })

        // Calculate buckets
        const actualBuckets = { zero: 0, one: 0, twoToFour: 0, fiveToNineteen: 0, twentyPlus: 0 }
        
        campaignLeadsData.forEach(lead => {
            const callsMade = callResultsMap[lead.leadid] || 0
            
            if (callsMade === 0) actualBuckets.zero++
            else if (callsMade === 1) actualBuckets.one++
            else if (callsMade >= 2 && callsMade <= 4) actualBuckets.twoToFour++
            else if (callsMade >= 5 && callsMade <= 19) actualBuckets.fiveToNineteen++
            else if (callsMade >= 20) actualBuckets.twentyPlus++
        })

        // Compare with expected
        let isValid = true
        Object.keys(expectedBuckets).forEach(bucket => {
            if (actualBuckets[bucket] !== expectedBuckets[bucket]) {
                this.errors.push(`${context}: Bucket ${bucket} = ${actualBuckets[bucket]}, expected ${expectedBuckets[bucket]}`)
                isValid = false
            }
        })

        return isValid
    }

    /**
     * Validate performance metrics
     */
    validatePerformance(startTime, endTime, recordCount, maxTimePerRecord = 0.1, context = '') {
        const totalTime = endTime - startTime
        const timePerRecord = totalTime / recordCount
        
        if (timePerRecord > maxTimePerRecord) {
            this.warnings.push(`${context}: Performance concern - ${timePerRecord.toFixed(3)}ms per record (max: ${maxTimePerRecord}ms)`)
        }
        
        return timePerRecord <= maxTimePerRecord
    }

    /**
     * Validate hierarchical total consistency
     */
    validateHierarchicalTotals(result, context = '') {
        let isValid = true
        
        result.stages.forEach(stage => {
            // Stage totals should equal sum of reporting group totals
            const rgTotalAttempts = stage.reportingGroups.reduce((sum, rg) => sum + rg.callAttempts, 0)
            const rgTotalLeads = stage.reportingGroups.reduce((sum, rg) => sum + rg.leads, 0)
            
            if (stage.callAttempts !== rgTotalAttempts) {
                this.errors.push(`${context}: Stage ${stage.id} attempts (${stage.callAttempts}) != sum of RG attempts (${rgTotalAttempts})`)
                isValid = false
            }
            
            if (stage.leadsWithAttempts !== rgTotalLeads) {
                this.errors.push(`${context}: Stage ${stage.id} leads (${stage.leadsWithAttempts}) != sum of RG leads (${rgTotalLeads})`)
                isValid = false
            }
            
            stage.reportingGroups.forEach(rg => {
                // Reporting group totals should equal sum of lead type totals
                const ltTotalAttempts = rg.leadTypes.reduce((sum, lt) => sum + lt.callAttempts, 0)
                const ltTotalLeads = rg.leadTypes.reduce((sum, lt) => sum + lt.leadsWithAttempts, 0)
                
                if (rg.callAttempts !== ltTotalAttempts) {
                    this.errors.push(`${context}: RG ${rg.id} attempts (${rg.callAttempts}) != sum of LT attempts (${ltTotalAttempts})`)
                    isValid = false
                }
                
                if (rg.leads !== ltTotalLeads) {
                    this.errors.push(`${context}: RG ${rg.id} leads (${rg.leads}) != sum of LT leads (${ltTotalLeads})`)
                    isValid = false
                }
            })
        })
        
        return isValid
    }

    /**
     * Validate that no leads are missing from hierarchy
     */
    validateLeadCoverage(callAttemptsData, campaignLeadsData, context = '') {
        // Get all leads that should appear in the hierarchy
        const expectedLeads = new Set(campaignLeadsData.map(cl => cl.leadid))
        
        // Get all leads that appear in call attempts detail rows
        const actualLeads = new Set()
        callAttemptsData.filter(row => row.stageId && row.reportingGroupId && row.skillId)
            .forEach(row => {
                // This is a detail row - find matching leads
                campaignLeadsData.forEach(lead => {
                    if (lead.stageId === row.stageId && 
                        lead.reportingGroupId === row.reportingGroupId && 
                        lead.skillId === row.skillId) {
                        actualLeads.add(lead.leadid)
                    }
                })
            })
        
        const missingLeads = [...expectedLeads].filter(id => !actualLeads.has(id))
        if (missingLeads.length > 0) {
            this.errors.push(`${context}: Missing leads from hierarchy: ${missingLeads.join(', ')}`)
            return false
        }
        
        return true
    }

    /**
     * Generate comprehensive validation report
     */
    generateReport() {
        const report = {
            isValid: this.errors.length === 0,
            errorCount: this.errors.length,
            warningCount: this.warnings.length,
            errors: this.errors,
            warnings: this.warnings,
            summary: this.errors.length === 0 ? 'All validations passed' : `${this.errors.length} validation errors found`
        }
        
        return report
    }

    /**
     * Reset validator state
     */
    reset() {
        this.errors = []
        this.warnings = []
    }

    /**
     * Static method for quick validation
     */
    static validateQuick(query1Results, query2Results, query3Results) {
        const validator = new DataValidator()
        validator.validateQueryTotals(query1Results, query2Results, query3Results, 'Quick Validation')
        return validator.generateReport()
    }
}

/**
 * Test data generators for consistent testing
 */
class TestDataGenerator {
    static generateCampaignLeads(count, campaignId = 1, stageCount = 3, skillCount = 3) {
        const leads = []
        for (let i = 1; i <= count; i++) {
            leads.push({
                leadid: i,
                campaignid: campaignId,
                currentCampaignStageId: 1 + (i % stageCount),
                tfSkillId: 101 + (i % skillCount),
                tfSubskillid: 201 + (i % (skillCount * 2))
            })
        }
        return leads
    }

    static generateCallResults(leadIds, campaignId = 1, callProbability = 0.7) {
        const results = []
        let resultId = 1
        
        leadIds.forEach(leadId => {
            if (Math.random() < callProbability) {
                const numCalls = Math.floor(Math.random() * 5) + 1 // 1-5 calls
                for (let i = 0; i < numCalls; i++) {
                    results.push({
                        id: resultId++,
                        leadid: leadId,
                        campaignid: campaignId,
                        createdAt: `2025-01-${(i + 1 < 10 ? '0' : '') + (i + 1)}`
                    })
                }
            }
        })
        
        return results
    }

    static generateCallAttempts(leadIds, campaignId = 1) {
        const attempts = []
        let attemptId = 1
        
        leadIds.forEach(leadId => {
            const numAttempts = 1 + Math.floor(Math.random() * 3) // 1-3 attempts
            for (let i = 0; i < numAttempts; i++) {
                attempts.push({
                    id: attemptId++,
                    leadid: leadId,
                    campaignid: campaignId,
                    createdfromdtuuid: `rule${1 + (leadId % 2)}`
                })
            }
        })
        
        return attempts
    }
}

/**
 * Performance benchmarking utilities
 */
class PerformanceBenchmark {
    constructor(name) {
        this.name = name
        this.startTime = null
        this.endTime = null
        this.metrics = {}
    }

    start() {
        this.startTime = Date.now()
        return this
    }

    end() {
        this.endTime = Date.now()
        return this
    }

    addMetric(name, value) {
        this.metrics[name] = value
        return this
    }

    getResults() {
        const duration = this.endTime - this.startTime
        return {
            name: this.name,
            duration: duration,
            startTime: this.startTime,
            endTime: this.endTime,
            metrics: this.metrics,
            summary: `${this.name}: ${duration}ms`
        }
    }

    static benchmark(name, fn) {
        const bench = new PerformanceBenchmark(name)
        bench.start()
        const result = fn()
        bench.end()
        return { result, benchmark: bench.getResults() }
    }
}

module.exports = {
    DataValidator,
    TestDataGenerator,
    PerformanceBenchmark
}
