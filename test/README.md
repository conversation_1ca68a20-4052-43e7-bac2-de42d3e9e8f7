# CallAnalysisV2 Optimization Test Suite

This comprehensive test suite validates the CallAnalysisV2 endpoint.

## 🎯 Test Objectives

1. **Data Consistency**: Ensure dial count buckets sum correctly across all query results
2. **Performance Validation**: Verify the optimization handles large datasets efficiently  
3. **Accuracy Testing**: Validate that totals match across queries
4. **Edge Case Handling**: Test boundary conditions and unusual data distributions

## 📁 Test Structure

```
test/
├── unit/
│   └── processStageViewV2Data.test.js     # Unit tests for core processing function
├── integration/
│   └── callanalysisv2.integration.test.js # Integration tests with mock data
├── utils/
│   └── dataValidation.js                  # Validation utilities and test data generators
├── runCallAnalysisV2Tests.js              # Comprehensive test runner
├── package.json                           # Test dependencies and scripts
└── README.md                              # This file
```

## 🚀 Quick Start

### Prerequisites
```bash
cd test/
npm install
```

### Run All Tests
```bash
npm test
```

### Run Specific Test Types
```bash
# Unit tests only
npm run test:unit

# Integration tests only  
npm run test:integration

# Validation and performance tests
npm run test:validation

# Performance metrics only
npm run test:performance

# Watch mode for development
npm run test:watch
```

## 📊 Test Categories

### 1. Unit Tests (`test:unit`)
- Tests the `processStageViewV2Data` function in isolation
- Validates dial count bucket calculations
- Tests stage processing logic
- Verifies data structure transformations

**Key Test Cases:**
- Zero call scenarios
- Distribution across all dial count buckets
- Stage-level aggregations
- Skill/reporting group hierarchies

### 2. Integration Tests (`test:integration`)
- Tests the complete 3-query workflow
- Validates query result consistency
- Tests referential integrity across queries
- Performance testing with larger datasets

**Key Test Cases:**
- Query total consistency validation
- Call results to campaign leads mapping
- Stage grouping accuracy
- Edge case handling (leads without calls, multiple calls)

### 3. Validation Tests (`test:validation`)
- Comprehensive end-to-end validation
- Performance benchmarking
- Scale testing (up to 10,000 records)
- Data consistency across multiple scenarios

**Test Configurations:**
- Small Dataset: 100 leads (basic functionality)
- Medium Dataset: 1,000 leads (performance validation)
- Large Dataset: 10,000 leads (scale testing)
- Edge Cases: 50 leads (boundary conditions)

## 🔍 What the Tests Validate

### Data Consistency Checks
- ✅ Dial count buckets sum to total campaign leads
- ✅ All call result leads exist in campaign leads
- ✅ Query 1 total matches Query 3 lead count
- ✅ Stage groupings are consistent across queries

### Performance Metrics
- ⚡ Processing time per lead
- 📊 Query execution time breakdown
- 🎯 Memory efficiency validation

### Accuracy Validation
- 🎲 Correct dial count bucket distribution
- 📋 Stage-level aggregation accuracy
- 🔗 Proper lead-to-call-result mapping
- 📊 Reporting group and skill hierarchies

## 🐛 Troubleshooting

### Common Issues

**Test Failures:**
```bash
# Check for data consistency issues
npm run test:validation

# Run with verbose output
DEBUG=* npm test
```

**Performance Issues:**
```bash
# Focus on performance metrics
npm run test:performance

# Profile specific test
node --prof test/runCallAnalysisV2Tests.js
```

### Debugging Tips

1. **Dial Count Bucket Mismatches**: Check the `calculateDialCountBuckets` function logic
2. **Query Total Inconsistencies**: Verify the WITH ROLLUP simulation in Query 1
3. **Performance Degradation**: Review the JavaScript processing optimization

## 📋 Test Output Example

```
🚀 CallAnalysisV2 Optimization Test Suite
=========================================

📊 Running Validation Tests...

Testing: Small Dataset (100 leads)
Purpose: Basic functionality test
  ✅ Duration: 8ms
  ⚡ Time per lead: 0.080ms
  📈 Query breakdown:
     - Query 1: 2ms
     - Query 2: 1ms
     - Query 3: 1ms
     - JS Processing: 4ms
  🎯 Validation: PASSED
  📊 Dial Count Distribution:
     - Zero calls: 30
     - One call: 25
     - 2-4 calls: 35
     - 5-19 calls: 10
     - 20+ calls: 0

📋 Final Test Report
===================

Total Tests: 4
Passed: 4
Failed: 0
Success Rate: 100.0%

🎉 All tests passed!
```

## 🔧 Customizing Tests

### Adding New Test Cases

1. **Unit Tests**: Add to `unit/processStageViewV2Data.test.js`
2. **Integration Tests**: Add to `integration/callanalysisv2.integration.test.js`
3. **Custom Validation**: Modify `runCallAnalysisV2Tests.js`

### Test Data Generation

Use the `TestDataGenerator` class in `utils/dataValidation.js`:

```javascript
const { TestDataGenerator } = require('./utils/dataValidation')

// Generate test data
const campaignLeads = TestDataGenerator.generateCampaignLeads(1000, campaignId)
const callResults = TestDataGenerator.generateCallResults(leadIds, campaignId, 0.7)
```
