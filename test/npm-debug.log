0 info it worked if it ends with ok
1 verbose cli [ '/Users/<USER>/.nvm/versions/node/v6.17.1/bin/node',
1 verbose cli   '/Users/<USER>/.nvm/versions/node/v6.17.1/bin/npm',
1 verbose cli   'run',
1 verbose cli   'test:unit' ]
2 info using npm@3.10.10
3 info using node@v6.17.1
4 verbose run-script [ 'pretest:unit', 'test:unit', 'posttest:unit' ]
5 info lifecycle callanalysisv2-tests@1.0.0~pretest:unit: callanalysisv2-tests@1.0.0
6 silly lifecycle callanalysisv2-tests@1.0.0~pretest:unit: no script for pretest:unit, continuing
7 info lifecycle callanalysisv2-tests@1.0.0~test:unit: callanalysisv2-tests@1.0.0
8 verbose lifecycle callanalysisv2-tests@1.0.0~test:unit: unsafe-perm in lifecycle true
9 verbose lifecycle callanalysisv2-tests@1.0.0~test:unit: PATH: /Users/<USER>/.nvm/versions/node/v6.17.1/lib/node_modules/npm/bin/node-gyp-bin:/Users/<USER>/Projects/dialerserver/test/node_modules/.bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Users/<USER>/.nvm/versions/node/v6.17.1/bin:/opt/homebrew/bin:/opt/homebrew/sbin
10 verbose lifecycle callanalysisv2-tests@1.0.0~test:unit: CWD: /Users/<USER>/Projects/dialerserver/test
11 silly lifecycle callanalysisv2-tests@1.0.0~test:unit: Args: [ '-c',
11 silly lifecycle   'mocha unit/processStageViewV2Data.test.js --reporter spec' ]
12 silly lifecycle callanalysisv2-tests@1.0.0~test:unit: Returned: code: 7  signal: null
13 info lifecycle callanalysisv2-tests@1.0.0~test:unit: Failed to exec test:unit script
14 verbose stack Error: callanalysisv2-tests@1.0.0 test:unit: `mocha unit/processStageViewV2Data.test.js --reporter spec`
14 verbose stack Exit status 7
14 verbose stack     at EventEmitter.<anonymous> (/Users/<USER>/.nvm/versions/node/v6.17.1/lib/node_modules/npm/lib/utils/lifecycle.js:255:16)
14 verbose stack     at emitTwo (events.js:106:13)
14 verbose stack     at EventEmitter.emit (events.js:191:7)
14 verbose stack     at ChildProcess.<anonymous> (/Users/<USER>/.nvm/versions/node/v6.17.1/lib/node_modules/npm/lib/utils/spawn.js:40:14)
14 verbose stack     at emitTwo (events.js:106:13)
14 verbose stack     at ChildProcess.emit (events.js:191:7)
14 verbose stack     at maybeClose (internal/child_process.js:920:16)
14 verbose stack     at Process.ChildProcess._handle.onexit (internal/child_process.js:230:5)
15 verbose pkgid callanalysisv2-tests@1.0.0
16 verbose cwd /Users/<USER>/Projects/dialerserver/test
17 error Darwin 24.6.0
18 error argv "/Users/<USER>/.nvm/versions/node/v6.17.1/bin/node" "/Users/<USER>/.nvm/versions/node/v6.17.1/bin/npm" "run" "test:unit"
19 error node v6.17.1
20 error npm  v3.10.10
21 error code ELIFECYCLE
22 error callanalysisv2-tests@1.0.0 test:unit: `mocha unit/processStageViewV2Data.test.js --reporter spec`
22 error Exit status 7
23 error Failed at the callanalysisv2-tests@1.0.0 test:unit script 'mocha unit/processStageViewV2Data.test.js --reporter spec'.
23 error Make sure you have the latest version of node.js and npm installed.
23 error If you do, this is most likely a problem with the callanalysisv2-tests package,
23 error not with npm itself.
23 error Tell the author that this fails on your system:
23 error     mocha unit/processStageViewV2Data.test.js --reporter spec
23 error You can get information on how to open an issue for this project with:
23 error     npm bugs callanalysisv2-tests
23 error Or if that isn't available, you can get their info via:
23 error     npm owner ls callanalysisv2-tests
23 error There is likely additional logging output above.
24 verbose exit [ 1, true ]
