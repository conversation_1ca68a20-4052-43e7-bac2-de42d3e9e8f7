const { expect } = require('chai')
const _ = require('underscore')

// Mock the processStageViewV2Data function for testing
// This would normally be imported from the controller
function processStageViewV2Data(callAttemptsData, campaignLeadsData, leadStatusData, suppressionsData, campaign, skills, rules) {
    var stages = []
    var totalAttempts = 0
    var totalLeads = 0
    var totalExhausted = 0
    var totalLeadsWithAttempts = 0
    var totalViable = 0
    var totalCallback = 0
    var totalDontContactUntil = 0
    var totalNoStage = 0
    var totalBadNumbers = 0
    var totalDialAttemptBuckets = {
        zero: 0,
        one: 0,
        twoToFour: 0,
        fiveToNineteen: 0,
        twentyPlus: 0
    }

    // No longer need callResultsMap since campaignLeadsData includes calls_made directly

    // Helper function to calculate dial count buckets from separated data
    function calculateDialCountBuckets(stageId, reportingGroupId, skillId) {
        var buckets = { zero: 0, one: 0, twoToFour: 0, fiveToNineteen: 0, twentyPlus: 0 }

        campaignLeadsData.forEach(lead => {
            // Add null check for defensive programming
            if (!lead) return

            // Match the grouping criteria (note: field names changed in enhanced query)
            var matchesStage = !stageId || lead.campaignStageId === stageId
            var matchesReportingGroup = !reportingGroupId || lead.reportingGroupId === reportingGroupId
            var matchesSkill = !skillId || lead.skillId === skillId

            if (matchesStage && matchesReportingGroup && matchesSkill) {
                // Use calls_made directly from enhanced campaignLeadsData
                var callsMade = parseInt(lead.calls_made || 0)

                if (callsMade === 0) buckets.zero++
                else if (callsMade === 1) buckets.one++
                else if (callsMade >= 2 && callsMade <= 4) buckets.twoToFour++
                else if (callsMade >= 5 && callsMade <= 19) buckets.fiveToNineteen++
                else if (callsMade >= 20) buckets.twentyPlus++
            }
        })

        return buckets
    }

    // Process call attempts data - simplified version for testing
    callAttemptsData.forEach(row => {
        try {
            if (!row.stageId) {
                totalAttempts = row.attempts
                totalLeadsWithAttempts = row.leads
                // Calculate total dial count buckets from separated data
                totalDialAttemptBuckets = calculateDialCountBuckets(null, null, null)
                return
            }

            var stage = _.findWhere(campaign.campaignstages, {
                id: row.stageId
            })

            var existingStage = _.findWhere(stages, {
                id: row.stageId
            })

            if (!existingStage) {
                // Create new stage
                var newstage = {
                    id: row.stageId,
                    name: stage ? stage.name : '',
                    leadsWithAttempts: row.leads,
                    callAttempts: row.attempts,
                    suppressions: 0, // Initialize suppressions
                    dialAttemptBuckets: calculateDialCountBuckets(row.stageId, null, null),
                    reportingGroups: []
                }
                stages.push(newstage)
                existingStage = newstage
            } else {
                existingStage.leadsWithAttempts = row.leads
                existingStage.callAttempts = row.attempts
            }

            // Process reporting groups and lead types if present
            if (row.reportingGroupId && row.skillId) {
                var reportingGroup = _.findWhere(existingStage.reportingGroups, { id: row.reportingGroupId })
                if (!reportingGroup) {
                    var skill = _.findWhere(skills, { id: row.reportingGroupId })
                    reportingGroup = {
                        id: row.reportingGroupId,
                        name: skill ? skill.name : 'Unknown Group',
                        suppressions: 0, // Initialize suppressions
                        leadTypes: []
                    }
                    existingStage.reportingGroups.push(reportingGroup)
                }

                var leadType = _.findWhere(reportingGroup.leadTypes, { id: row.skillId })
                if (!leadType) {
                    var subskill = _.findWhere(skills, { id: row.skillId })
                    leadType = {
                        id: row.skillId,
                        name: subskill ? subskill.name : 'Unknown Lead Type',
                        callAttempts: row.attempts,
                        leadsWithAttempts: row.leads,
                        suppressed: 0  // Initialize suppressed count
                    }
                    reportingGroup.leadTypes.push(leadType)
                } else {
                    leadType.callAttempts = row.attempts
                    leadType.leadsWithAttempts = row.leads
                }
            }
        } catch (err) {
            console.log('Error processing call attempts row:', err)
        }
    })

    // Process suppressions data
    var suppressedTotal = 0
    if (suppressionsData && Array.isArray(suppressionsData)) {
        suppressionsData.forEach(function(row) {
            try {
                if (!row || typeof row !== 'object') {
                    return
                }

                // Validate suppressed count
                if (row.suppressed !== undefined && row.suppressed !== null && (isNaN(parseInt(row.suppressed)) || parseInt(row.suppressed) < 0)) {
                    return // Skip invalid suppressed values
                }

                // ROLLUP Level 0: Grand total (all nulls)
                if (!row.stageId && !row.reportingGroupId && !row.skillId) {
                    suppressedTotal = parseInt(row.suppressed || 0)
                    return
                }

                // ROLLUP Level 1: Stage totals (reportingGroupId = null, skillId = null)
                if (!row.reportingGroupId && !row.skillId) {
                    var existingStage = _.findWhere(stages, { id: row.stageId })
                    if (existingStage) {
                        existingStage.suppressions = parseInt(row.suppressed || 0)
                    }
                    return
                }

                // ROLLUP Level 2: Reporting group totals (skillId = null)
                if (!row.skillId) {
                    var existingStage = _.findWhere(stages, { id: row.stageId })
                    if (existingStage) {
                        var existingReportingGroup = _.findWhere(existingStage.reportingGroups, { id: row.reportingGroupId })
                        if (existingReportingGroup) {
                            existingReportingGroup.suppressions = parseInt(row.suppressed || 0)
                        }
                    }
                    return
                }

                // ROLLUP Level 3: Lead type details (all fields populated)
                var existingStage = _.findWhere(stages, { id: row.stageId })
                if (existingStage) {
                    var existingReportingGroup = _.findWhere(existingStage.reportingGroups, { id: row.reportingGroupId })
                    if (existingReportingGroup) {
                        var existingLeadType = _.findWhere(existingReportingGroup.leadTypes, { id: row.skillId })
                        if (existingLeadType) {
                            existingLeadType.suppressed = parseInt(row.suppressed || 0)
                        } else {
                            // Create lead type if it doesn't exist (for suppressions only)
                            var subskill = _.findWhere(skills, { id: row.skillId })
                            var suppressedCount = parseInt(row.suppressed || 0)
                            existingReportingGroup.leadTypes.push({
                                id: row.skillId,
                                name: subskill ? subskill.name : 'Unknown Lead Type',
                                suppressed: suppressedCount,
                                callAttempts: 0,
                                leadsWithAttempts: 0
                            })
                        }
                    }
                }
            } catch (err) {
                console.log('Error processing suppression row:', row, err)
            }
        })


    }

    return {
        stages: stages,
        totalAttempts: totalAttempts,
        totalLeads: totalLeads,
        totalLeadsWithAttempts: totalLeadsWithAttempts,
        totalDialAttemptBuckets: totalDialAttemptBuckets,
        totalExhausted: totalExhausted,
        totalViable: totalViable,
        totalCallback: totalCallback,
        totalDontContactUntil: totalDontContactUntil,
        totalNoStage: totalNoStage,
        totalBadNumbers: totalBadNumbers,
        suppressedTotal: suppressedTotal
    }
}

describe('processStageViewV2Data', function() {
    let mockCampaign, mockSkills, mockRules

    beforeEach(function() {
        mockCampaign = {
            campaignstages: [
                { id: 1, name: 'New Leads' },
                { id: 2, name: 'Follow Up' },
                { id: 3, name: 'Qualified' }
            ]
        }

        mockSkills = [
            { id: 101, name: 'Skill A' },
            { id: 102, name: 'Skill B' },
            { id: 201, name: 'Subskill A1' },
            { id: 202, name: 'Subskill A2' }
        ]

        mockRules = []
    })

    describe('Dial Count Bucket Calculations', function() {
        it('should correctly calculate dial count buckets for zero calls', function() {
            const callAttemptsData = [
                { stageId: null, attempts: 100, leads: 50 }, // Total row
                { stageId: 1, reportingGroupId: 101, skillId: 201, attempts: 50, leads: 25 }
            ]



            const campaignLeadsData = [
                { leadid: 1, stageId: 1, reportingGroupId: 101, skillId: 201 },
                { leadid: 2, stageId: 1, reportingGroupId: 101, skillId: 201 },
                { leadid: 3, stageId: 1, reportingGroupId: 101, skillId: 201 }
            ]

            const result = processStageViewV2Data(
                callAttemptsData,
                campaignLeadsData,
                [], [],
                mockCampaign,
                mockSkills,
                mockRules
            )

            expect(result.totalDialAttemptBuckets.zero).to.equal(3)
            expect(result.totalDialAttemptBuckets.one).to.equal(0)
            expect(result.totalDialAttemptBuckets.twoToFour).to.equal(0)
            expect(result.totalDialAttemptBuckets.fiveToNineteen).to.equal(0)
            expect(result.totalDialAttemptBuckets.twentyPlus).to.equal(0)
        })

        it('should correctly distribute leads across dial count buckets', function() {
            const callAttemptsData = [
                { stageId: null, attempts: 200, leads: 100 }, // Total row
                { stageId: 1, reportingGroupId: 101, skillId: 201, attempts: 100, leads: 50 }
            ]

            // Enhanced campaignLeadsData now includes calls_made from the optimized query
            const campaignLeadsData = [
                { leadid: 1, campaignStageId: 1, reportingGroupId: 101, skillId: 201, calls_made: 0 },   // zero bucket
                { leadid: 2, campaignStageId: 1, reportingGroupId: 101, skillId: 201, calls_made: 1 },   // one bucket
                { leadid: 3, campaignStageId: 1, reportingGroupId: 101, skillId: 201, calls_made: 3 },   // twoToFour bucket
                { leadid: 4, campaignStageId: 1, reportingGroupId: 101, skillId: 201, calls_made: 10 },  // fiveToNineteen bucket
                { leadid: 5, campaignStageId: 1, reportingGroupId: 101, skillId: 201, calls_made: 25 },  // twentyPlus bucket
                { leadid: 6, campaignStageId: 1, reportingGroupId: 101, skillId: 201, calls_made: 0 }    // zero bucket
            ]

            const result = processStageViewV2Data(
                callAttemptsData,
                campaignLeadsData,
                [], [],
                mockCampaign,
                mockSkills,
                mockRules
            )

            expect(result.totalDialAttemptBuckets.zero).to.equal(2) // leadid 1 + leadid 6
            expect(result.totalDialAttemptBuckets.one).to.equal(1)  // leadid 2
            expect(result.totalDialAttemptBuckets.twoToFour).to.equal(1) // leadid 3
            expect(result.totalDialAttemptBuckets.fiveToNineteen).to.equal(1) // leadid 4
            expect(result.totalDialAttemptBuckets.twentyPlus).to.equal(1) // leadid 5

            // Total should equal number of campaign leads
            const totalBuckets = result.totalDialAttemptBuckets.zero + 
                               result.totalDialAttemptBuckets.one + 
                               result.totalDialAttemptBuckets.twoToFour + 
                               result.totalDialAttemptBuckets.fiveToNineteen + 
                               result.totalDialAttemptBuckets.twentyPlus
            expect(totalBuckets).to.equal(6)
        })
    })

    describe('Stage Processing', function() {
        it('should correctly process stages and calculate stage-level dial buckets', function() {
            const callAttemptsData = [
                { stageId: null, attempts: 300, leads: 150 }, // Total row
                { stageId: 1, reportingGroupId: 101, skillId: 201, attempts: 100, leads: 50 },
                { stageId: 2, reportingGroupId: 101, skillId: 201, attempts: 200, leads: 100 }
            ]

            const callResultsData = [
                { leadid: 1, campaignStageId: 1, calls_made: 1 },   // Stage 1 lead
                { leadid: 2, campaignStageId: 1, calls_made: 3 },   // Stage 1 lead
                { leadid: 3, campaignStageId: 2, calls_made: 5 },   // Stage 2 lead
                { leadid: 4, campaignStageId: 2, calls_made: 15 }   // Stage 2 lead
            ]

            const campaignLeadsData = [
                { leadid: 1, stageId: 1, reportingGroupId: 101, skillId: 201 },
                { leadid: 2, stageId: 1, reportingGroupId: 101, skillId: 201 },
                { leadid: 3, stageId: 2, reportingGroupId: 101, skillId: 201 },
                { leadid: 4, stageId: 2, reportingGroupId: 101, skillId: 201 }
            ]

            const result = processStageViewV2Data(
                callAttemptsData, 
                callResultsData, 
                campaignLeadsData, 
                [], [], 
                mockCampaign, 
                mockSkills, 
                mockRules
            )

            expect(result.stages).to.have.length(2)
            
            const stage1 = _.findWhere(result.stages, { id: 1 })
            const stage2 = _.findWhere(result.stages, { id: 2 })
            
            expect(stage1).to.exist
            expect(stage2).to.exist
            
            // Stage 1 should have: 1 lead with 1 call, 1 lead with 3 calls
            expect(stage1.dialAttemptBuckets.one).to.equal(1)
            expect(stage1.dialAttemptBuckets.twoToFour).to.equal(1)
            
            // Stage 2 should have: 1 lead with 5 calls, 1 lead with 15 calls
            expect(stage2.dialAttemptBuckets.fiveToNineteen).to.equal(2)
        })

        it('should correctly handle stage transitions and only count calls made in current stage', function() {
            // This is the critical test for the stage-specific counting logic
            // Lead 1: Started in stage 1 (2 calls), moved to stage 2 (3 calls) - should count only stage 2 calls
            // Lead 2: Started in stage 1 (1 call), moved to stage 2 (0 calls) - should count only stage 2 calls (zero)
            // Lead 3: Only in stage 1 (4 calls) - should count stage 1 calls
            // Lead 4: Only in stage 2 (1 call) - should count stage 2 calls

            const callAttemptsData = [
                { stageId: null, attempts: 11, leads: 4 }, // Total row
                { stageId: 1, reportingGroupId: 101, skillId: 201, attempts: 7, leads: 2 }, // Stage 1 totals
                { stageId: 2, reportingGroupId: 101, skillId: 201, attempts: 4, leads: 2 }  // Stage 2 totals
            ]

            const callResultsData = [
                // Lead 1: 2 calls in stage 1, 3 calls in stage 2 (currently in stage 2)
                { leadid: 1, campaignStageId: 1, calls_made: 2 },
                { leadid: 1, campaignStageId: 2, calls_made: 3 },

                // Lead 2: 1 call in stage 1, 0 calls in stage 2 (currently in stage 2)
                { leadid: 2, campaignStageId: 1, calls_made: 1 },
                // No entry for lead 2 in stage 2 = 0 calls

                // Lead 3: 4 calls in stage 1 (currently in stage 1)
                { leadid: 3, campaignStageId: 1, calls_made: 4 },

                // Lead 4: 1 call in stage 2 (currently in stage 2)
                { leadid: 4, campaignStageId: 2, calls_made: 1 }
            ]

            const campaignLeadsData = [
                { leadid: 1, stageId: 2, reportingGroupId: 101, skillId: 201 }, // Currently in stage 2
                { leadid: 2, stageId: 2, reportingGroupId: 101, skillId: 201 }, // Currently in stage 2
                { leadid: 3, stageId: 1, reportingGroupId: 101, skillId: 201 }, // Currently in stage 1
                { leadid: 4, stageId: 2, reportingGroupId: 101, skillId: 201 }  // Currently in stage 2
            ]

            const result = processStageViewV2Data(
                callAttemptsData,
                callResultsData,
                campaignLeadsData,
                [], [],
                mockCampaign,
                mockSkills,
                mockRules
            )

            expect(result.stages).to.have.length(2)

            const stage1 = result.stages.find(s => s.id === 1)
            const stage2 = result.stages.find(s => s.id === 2)

            expect(stage1).to.exist
            expect(stage2).to.exist

            // Stage 1 should only count Lead 3 (4 calls = twoToFour bucket)
            expect(stage1.dialAttemptBuckets.zero).to.equal(0)
            expect(stage1.dialAttemptBuckets.one).to.equal(0)
            expect(stage1.dialAttemptBuckets.twoToFour).to.equal(1)
            expect(stage1.dialAttemptBuckets.fiveToNineteen).to.equal(0)
            expect(stage1.dialAttemptBuckets.twentyPlus).to.equal(0)

            // Stage 2 should count:
            // - Lead 1: 3 calls (twoToFour bucket)
            // - Lead 2: 0 calls (zero bucket)
            // - Lead 4: 1 call (one bucket)
            expect(stage2.dialAttemptBuckets.zero).to.equal(1)  // Lead 2
            expect(stage2.dialAttemptBuckets.one).to.equal(1)   // Lead 4
            expect(stage2.dialAttemptBuckets.twoToFour).to.equal(1) // Lead 1
            expect(stage2.dialAttemptBuckets.fiveToNineteen).to.equal(0)
            expect(stage2.dialAttemptBuckets.twentyPlus).to.equal(0)

            // Verify totals
            const totalZero = stage1.dialAttemptBuckets.zero + stage2.dialAttemptBuckets.zero
            const totalOne = stage1.dialAttemptBuckets.one + stage2.dialAttemptBuckets.one
            const totalTwoToFour = stage1.dialAttemptBuckets.twoToFour + stage2.dialAttemptBuckets.twoToFour

            expect(result.totalDialAttemptBuckets.zero).to.equal(totalZero)
            expect(result.totalDialAttemptBuckets.one).to.equal(totalOne)
            expect(result.totalDialAttemptBuckets.twoToFour).to.equal(totalTwoToFour)
        })
    })

    describe('Edge Cases and Error Handling', function() {
        it('should handle leads with calls in previous stages but none in current stage', function() {
            const callAttemptsData = [
                { stageId: null, attempts: 5, leads: 2 }, // Total row
                { stageId: 2, reportingGroupId: 101, skillId: 201, attempts: 0, leads: 2 } // Stage 2 totals
            ]

            const callResultsData = [
                // Lead has calls in stage 1 but is now in stage 2 with no calls
                { leadid: 1, campaignStageId: 1, calls_made: 5 },
                { leadid: 2, campaignStageId: 1, calls_made: 3 }
                // No calls for these leads in stage 2
            ]

            const campaignLeadsData = [
                { leadid: 1, stageId: 2, reportingGroupId: 101, skillId: 201 }, // Currently in stage 2
                { leadid: 2, stageId: 2, reportingGroupId: 101, skillId: 201 }  // Currently in stage 2
            ]

            const result = processStageViewV2Data(
                callAttemptsData,
                callResultsData,
                campaignLeadsData,
                [], [],
                mockCampaign,
                mockSkills,
                mockRules
            )

            const stage2 = result.stages.find(s => s.id === 2)
            expect(stage2).to.exist

            // Both leads should be in zero bucket since they have no calls in current stage
            expect(stage2.dialAttemptBuckets.zero).to.equal(2)
            expect(stage2.dialAttemptBuckets.one).to.equal(0)
            expect(stage2.dialAttemptBuckets.twoToFour).to.equal(0)
        })

        it('should handle missing call results data gracefully', function() {
            const callAttemptsData = [
                { stageId: null, attempts: 0, leads: 3 }, // Total row
                { stageId: 1, reportingGroupId: 101, skillId: 201, attempts: 0, leads: 3 }
            ]

            const callResultsData = [] // No call results at all

            const campaignLeadsData = [
                { leadid: 1, stageId: 1, reportingGroupId: 101, skillId: 201 },
                { leadid: 2, stageId: 1, reportingGroupId: 101, skillId: 201 },
                { leadid: 3, stageId: 1, reportingGroupId: 101, skillId: 201 }
            ]

            const result = processStageViewV2Data(
                callAttemptsData,
                callResultsData,
                campaignLeadsData,
                [], [],
                mockCampaign,
                mockSkills,
                mockRules
            )

            const stage1 = result.stages.find(s => s.id === 1)
            expect(stage1).to.exist

            // All leads should be in zero bucket
            expect(stage1.dialAttemptBuckets.zero).to.equal(3)
            expect(stage1.dialAttemptBuckets.one).to.equal(0)
        })

        it('should handle invalid call results data', function() {
            const callAttemptsData = [
                { stageId: null, attempts: 10, leads: 2 }, // Total row
                { stageId: 1, reportingGroupId: 101, skillId: 201, attempts: 10, leads: 2 }
            ]

            const callResultsData = [
                { leadid: 1, campaignStageId: 1, calls_made: 2 },
                { leadid: 2, campaignStageId: 1, calls_made: null }, // Invalid data
                { leadid: 3, campaignStageId: 1 }, // Missing calls_made field
                null, // Invalid row
                { leadid: 4, campaignStageId: 1, calls_made: -1 } // Negative calls
            ]

            const campaignLeadsData = [
                { leadid: 1, stageId: 1, reportingGroupId: 101, skillId: 201 },
                { leadid: 2, stageId: 1, reportingGroupId: 101, skillId: 201 }
            ]

            const result = processStageViewV2Data(
                callAttemptsData,
                callResultsData,
                campaignLeadsData,
                [], [],
                mockCampaign,
                mockSkills,
                mockRules
            )

            const stage1 = result.stages.find(s => s.id === 1)
            expect(stage1).to.exist

            // Lead 1: 2 calls (twoToFour), Lead 2: invalid data treated as 0 (zero)
            expect(stage1.dialAttemptBuckets.zero).to.equal(1)
            expect(stage1.dialAttemptBuckets.twoToFour).to.equal(1)
        })
    })

    describe('Suppressed Leads Aggregation', function() {
        it('should correctly process ROLLUP suppressed leads at all levels', function() {
            const callAttemptsData = [
                { stageId: null, attempts: 100, leads: 50 }, // Total row
                { stageId: 1, reportingGroupId: 101, skillId: 201, attempts: 30, leads: 15 },
                { stageId: 1, reportingGroupId: 101, skillId: 202, attempts: 20, leads: 10 },
                { stageId: 1, reportingGroupId: 102, skillId: 203, attempts: 50, leads: 25 },
                { stageId: 2, reportingGroupId: 101, skillId: 201, attempts: 40, leads: 20 }
            ]

            const suppressionsData = [
                { stageId: null, reportingGroupId: null, skillId: null, suppressed: 25 }, // Grand total
                { stageId: 1, reportingGroupId: null, skillId: null, suppressed: 15 }, // Stage 1 total (ROLLUP)
                { stageId: 1, reportingGroupId: 101, skillId: null, suppressed: 8 }, // Stage 1, RG 101 total (ROLLUP)
                { stageId: 1, reportingGroupId: 101, skillId: 201, suppressed: 5 }, // Detail row
                { stageId: 1, reportingGroupId: 101, skillId: 202, suppressed: 3 }, // Detail row
                { stageId: 1, reportingGroupId: 102, skillId: null, suppressed: 7 }, // Stage 1, RG 102 total (ROLLUP)
                { stageId: 1, reportingGroupId: 102, skillId: 203, suppressed: 7 }, // Detail row
                { stageId: 2, reportingGroupId: null, skillId: null, suppressed: 10 }, // Stage 2 total (ROLLUP)
                { stageId: 2, reportingGroupId: 101, skillId: null, suppressed: 10 }, // Stage 2, RG 101 total (ROLLUP)
                { stageId: 2, reportingGroupId: 101, skillId: 201, suppressed: 10 } // Detail row
            ]

            const result = processStageViewV2Data(
                callAttemptsData,
                [],
                [],
                [],
                suppressionsData,
                mockCampaign,
                mockSkills,
                []
            )

            // Check grand total
            expect(result.suppressedTotal).to.equal(25)

            // Check stage 1 aggregation
            const stage1 = result.stages.find(s => s.id === 1)
            expect(stage1).to.exist
            expect(stage1.suppressions).to.equal(15) // 5 + 3 + 7

            // Check stage 1 reporting groups
            const stage1RG101 = stage1.reportingGroups.find(rg => rg.id === 101)
            expect(stage1RG101).to.exist
            expect(stage1RG101.suppressions).to.equal(8) // 5 + 3

            const stage1RG102 = stage1.reportingGroups.find(rg => rg.id === 102)
            expect(stage1RG102).to.exist
            expect(stage1RG102.suppressions).to.equal(7) // 7

            // Check stage 1 lead types
            const stage1RG101LT201 = stage1RG101.leadTypes.find(lt => lt.id === 201)
            expect(stage1RG101LT201).to.exist
            expect(stage1RG101LT201.suppressed).to.equal(5)

            const stage1RG101LT202 = stage1RG101.leadTypes.find(lt => lt.id === 202)
            expect(stage1RG101LT202).to.exist
            expect(stage1RG101LT202.suppressed).to.equal(3)

            // Check stage 2 aggregation
            const stage2 = result.stages.find(s => s.id === 2)
            expect(stage2).to.exist
            expect(stage2.suppressions).to.equal(10) // 10

            // Check stage 2 reporting group
            const stage2RG101 = stage2.reportingGroups.find(rg => rg.id === 101)
            expect(stage2RG101).to.exist
            expect(stage2RG101.suppressions).to.equal(10) // 10

            // Check stage 2 lead type
            const stage2RG101LT201 = stage2RG101.leadTypes.find(lt => lt.id === 201)
            expect(stage2RG101LT201).to.exist
            expect(stage2RG101LT201.suppressed).to.equal(10)
        })

        it('should handle suppressions for lead types that do not exist in call attempts data', function() {
            const callAttemptsData = [
                { stageId: null, attempts: 50, leads: 25 }, // Total row
                { stageId: 1, reportingGroupId: 101, skillId: 201, attempts: 50, leads: 25 }
            ]

            const suppressionsData = [
                { stageId: null, reportingGroupId: null, skillId: null, suppressed: 8 }, // Grand total
                { stageId: 1, reportingGroupId: null, skillId: null, suppressed: 8 }, // Stage 1 total (ROLLUP)
                { stageId: 1, reportingGroupId: 101, skillId: null, suppressed: 8 }, // Stage 1, RG 101 total (ROLLUP)
                { stageId: 1, reportingGroupId: 101, skillId: 201, suppressed: 3 },
                { stageId: 1, reportingGroupId: 101, skillId: 202, suppressed: 5 } // Lead type not in call attempts
            ]

            const result = processStageViewV2Data(
                callAttemptsData,
                [],
                [],
                [],
                suppressionsData,
                mockCampaign,
                mockSkills,
                []
            )

            // Check grand total
            expect(result.suppressedTotal).to.equal(8)

            // Check stage 1
            const stage1 = result.stages.find(s => s.id === 1)
            expect(stage1).to.exist
            expect(stage1.suppressions).to.equal(8) // 3 + 5

            // Check reporting group
            const rg101 = stage1.reportingGroups.find(rg => rg.id === 101)
            expect(rg101).to.exist
            expect(rg101.suppressions).to.equal(8) // 3 + 5

            // Check that both lead types exist
            expect(rg101.leadTypes).to.have.length(2)

            const lt201 = rg101.leadTypes.find(lt => lt.id === 201)
            expect(lt201).to.exist
            expect(lt201.suppressed).to.equal(3)

            const lt202 = rg101.leadTypes.find(lt => lt.id === 202)
            expect(lt202).to.exist
            expect(lt202.suppressed).to.equal(5)
        })

        it('should handle zero suppressions correctly', function() {
            const callAttemptsData = [
                { stageId: null, attempts: 100, leads: 50 }, // Total row
                { stageId: 1, reportingGroupId: 101, skillId: 201, attempts: 50, leads: 25 },
                { stageId: 2, reportingGroupId: 102, skillId: 202, attempts: 50, leads: 25 }
            ]

            const suppressionsData = [
                { stageId: null, reportingGroupId: null, skillId: null, suppressed: 0 }, // Grand total is 0
                { stageId: 1, reportingGroupId: null, skillId: null, suppressed: 0 }, // Stage 1 total (ROLLUP)
                { stageId: 1, reportingGroupId: 101, skillId: null, suppressed: 0 }, // Stage 1, RG 101 total (ROLLUP)
                { stageId: 1, reportingGroupId: 101, skillId: 201, suppressed: 0 }, // Detail row
                { stageId: 2, reportingGroupId: null, skillId: null, suppressed: 0 }, // Stage 2 total (ROLLUP)
                { stageId: 2, reportingGroupId: 102, skillId: null, suppressed: 0 }, // Stage 2, RG 102 total (ROLLUP)
                { stageId: 2, reportingGroupId: 102, skillId: 202, suppressed: 0 } // Detail row
            ]

            const result = processStageViewV2Data(
                callAttemptsData,
                [],
                [],
                [],
                suppressionsData,
                mockCampaign,
                mockSkills,
                []
            )

            // Check grand total
            expect(result.suppressedTotal).to.equal(0)

            // Check stages
            const stage1 = result.stages.find(s => s.id === 1)
            expect(stage1).to.exist
            expect(stage1.suppressions).to.equal(0)

            const stage2 = result.stages.find(s => s.id === 2)
            expect(stage2).to.exist
            expect(stage2.suppressions).to.equal(0)

            // Check reporting groups
            const stage1RG101 = stage1.reportingGroups.find(rg => rg.id === 101)
            expect(stage1RG101).to.exist
            expect(stage1RG101.suppressions).to.equal(0)

            const stage2RG102 = stage2.reportingGroups.find(rg => rg.id === 102)
            expect(stage2RG102).to.exist
            expect(stage2RG102.suppressions).to.equal(0)

            // Check lead types
            const stage1LT201 = stage1RG101.leadTypes.find(lt => lt.id === 201)
            expect(stage1LT201).to.exist
            expect(stage1LT201.suppressed).to.equal(0)

            const stage2LT202 = stage2RG102.leadTypes.find(lt => lt.id === 202)
            expect(stage2LT202).to.exist
            expect(stage2LT202.suppressed).to.equal(0)
        })

        it('should handle empty suppressions data', function() {
            const callAttemptsData = [
                { stageId: null, attempts: 100, leads: 50 }, // Total row
                { stageId: 1, reportingGroupId: 101, skillId: 201, attempts: 50, leads: 25 }
            ]

            const result = processStageViewV2Data(
                callAttemptsData,
                [],
                [],
                [],
                [], // Empty suppressions data
                mockCampaign,
                mockSkills,
                []
            )

            // Check grand total
            expect(result.suppressedTotal).to.equal(0)

            // Check stage
            const stage1 = result.stages.find(s => s.id === 1)
            expect(stage1).to.exist
            expect(stage1.suppressions).to.equal(0)

            // Check reporting group
            const rg101 = stage1.reportingGroups.find(rg => rg.id === 101)
            expect(rg101).to.exist
            expect(rg101.suppressions).to.equal(0)

            // Check lead type
            const lt201 = rg101.leadTypes.find(lt => lt.id === 201)
            expect(lt201).to.exist
            expect(lt201.suppressed).to.equal(0)
        })

        it('should handle invalid suppressions data gracefully', function() {
            const callAttemptsData = [
                { stageId: null, attempts: 100, leads: 50 }, // Total row
                { stageId: 1, reportingGroupId: 101, skillId: 201, attempts: 50, leads: 25 }
            ]

            const suppressionsData = [
                { stageId: null, reportingGroupId: null, skillId: null, suppressed: 5 }, // Grand total
                { stageId: 1, reportingGroupId: null, skillId: null, suppressed: 5 }, // Stage 1 total (ROLLUP)
                { stageId: 1, reportingGroupId: 101, skillId: null, suppressed: 5 }, // Stage 1, RG 101 total (ROLLUP)
                { stageId: 1, reportingGroupId: 101, skillId: 201, suppressed: 'invalid' }, // Invalid suppressed value
                null, // Invalid row
                { stageId: 1, reportingGroupId: 101, skillId: 202, suppressed: -1 }, // Negative value
                { stageId: 1, reportingGroupId: 101, skillId: 203, suppressed: 5 } // Valid row
            ]

            const result = processStageViewV2Data(
                callAttemptsData,
                [],
                [],
                [],
                suppressionsData,
                mockCampaign,
                mockSkills,
                []
            )

            // Check grand total
            expect(result.suppressedTotal).to.equal(5)

            // Check stage - should only include valid suppression (5)
            const stage1 = result.stages.find(s => s.id === 1)
            expect(stage1).to.exist
            expect(stage1.suppressions).to.equal(5)

            // Check reporting group
            const rg101 = stage1.reportingGroups.find(rg => rg.id === 101)
            expect(rg101).to.exist
            expect(rg101.suppressions).to.equal(5)

            // Check that only valid lead types are created
            const validLeadTypes = rg101.leadTypes.filter(lt => lt.suppressed > 0)
            expect(validLeadTypes).to.have.length(1)
            expect(validLeadTypes[0].id).to.equal(203)
            expect(validLeadTypes[0].suppressed).to.equal(5)
        })
    })
})
