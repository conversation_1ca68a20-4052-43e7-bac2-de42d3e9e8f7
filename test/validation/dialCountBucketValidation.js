const { expect } = require('chai')

/**
 * Comprehensive validation tests for dial count bucket calculations
 * These tests ensure that bucket totals are mathematically correct
 * and consistent across all hierarchy levels
 */

/**
 * Validates that dial count buckets sum correctly across all levels
 * @param {Object} result - The result from processStageViewV2Data
 * @param {Array} campaignLeadsData - The original campaign leads data
 * @param {Array} callResultsData - The original call results data
 */
function validateDialCountBuckets(result, campaignLeadsData, callResultsData) {
    const validationResults = {
        isValid: true,
        errors: [],
        warnings: [],
        summary: {}
    }

    try {
        // Create stage-specific call results map
        const callResultsMap = {}
        callResultsData.forEach(row => {
            if (row && row.leadid && row.campaignStageId) {
                const key = `${row.leadid}-${row.campaignStageId}`
                callResultsMap[key] = row.calls_made || 0
            }
        })

        // Calculate expected totals from raw data
        const expectedBuckets = { zero: 0, one: 0, twoToFour: 0, fiveToNineteen: 0, twentyPlus: 0 }
        const stageExpectedBuckets = {}

        campaignLeadsData.forEach(lead => {
            if (!lead || !lead.leadid || !lead.stageId) return

            const key = `${lead.leadid}-${lead.stageId}`
            const callsMade = callResultsMap[key] || 0

            // Count for grand total
            if (callsMade === 0) expectedBuckets.zero++
            else if (callsMade === 1) expectedBuckets.one++
            else if (callsMade >= 2 && callsMade <= 4) expectedBuckets.twoToFour++
            else if (callsMade >= 5 && callsMade <= 19) expectedBuckets.fiveToNineteen++
            else if (callsMade >= 20) expectedBuckets.twentyPlus++

            // Count for stage totals
            if (!stageExpectedBuckets[lead.stageId]) {
                stageExpectedBuckets[lead.stageId] = { zero: 0, one: 0, twoToFour: 0, fiveToNineteen: 0, twentyPlus: 0 }
            }

            if (callsMade === 0) stageExpectedBuckets[lead.stageId].zero++
            else if (callsMade === 1) stageExpectedBuckets[lead.stageId].one++
            else if (callsMade >= 2 && callsMade <= 4) stageExpectedBuckets[lead.stageId].twoToFour++
            else if (callsMade >= 5 && callsMade <= 19) stageExpectedBuckets[lead.stageId].fiveToNineteen++
            else if (callsMade >= 20) stageExpectedBuckets[lead.stageId].twentyPlus++
        })

        // Validate grand total buckets
        const bucketFields = ['zero', 'one', 'twoToFour', 'fiveToNineteen', 'twentyPlus']
        bucketFields.forEach(bucket => {
            if (result.totalDialAttemptBuckets[bucket] !== expectedBuckets[bucket]) {
                validationResults.errors.push(
                    `Grand total ${bucket} bucket mismatch: expected ${expectedBuckets[bucket]}, got ${result.totalDialAttemptBuckets[bucket]}`
                )
                validationResults.isValid = false
            }
        })

        // Validate stage-level buckets
        if (result.stages) {
            result.stages.forEach(stage => {
                if (stageExpectedBuckets[stage.id]) {
                    bucketFields.forEach(bucket => {
                        if (stage.dialAttemptBuckets[bucket] !== stageExpectedBuckets[stage.id][bucket]) {
                            validationResults.errors.push(
                                `Stage ${stage.id} ${bucket} bucket mismatch: expected ${stageExpectedBuckets[stage.id][bucket]}, got ${stage.dialAttemptBuckets[bucket]}`
                            )
                            validationResults.isValid = false
                        }
                    })
                }
            })
        }

        // Validate that stage buckets sum to grand total
        const stageBucketSums = { zero: 0, one: 0, twoToFour: 0, fiveToNineteen: 0, twentyPlus: 0 }
        if (result.stages) {
            result.stages.forEach(stage => {
                bucketFields.forEach(bucket => {
                    stageBucketSums[bucket] += stage.dialAttemptBuckets[bucket] || 0
                })
            })
        }

        bucketFields.forEach(bucket => {
            if (stageBucketSums[bucket] !== result.totalDialAttemptBuckets[bucket]) {
                validationResults.errors.push(
                    `Stage bucket sum mismatch for ${bucket}: stage sum ${stageBucketSums[bucket]} != grand total ${result.totalDialAttemptBuckets[bucket]}`
                )
                validationResults.isValid = false
            }
        })

        // Calculate total leads for validation (Node 6 compatible)
        const totalLeadsFromBuckets = Object.keys(expectedBuckets).reduce((sum, key) => sum + expectedBuckets[key], 0)
        const totalLeadsFromStages = Object.keys(stageBucketSums).reduce((sum, key) => sum + stageBucketSums[key], 0)

        validationResults.summary = {
            totalLeadsFromData: campaignLeadsData.length,
            totalLeadsFromBuckets,
            totalLeadsFromStages,
            expectedBuckets,
            actualBuckets: result.totalDialAttemptBuckets,
            stageBucketSums
        }

        // Validate total lead counts
        if (totalLeadsFromBuckets !== campaignLeadsData.length) {
            validationResults.warnings.push(
                `Total leads mismatch: ${campaignLeadsData.length} leads in data, ${totalLeadsFromBuckets} in buckets`
            )
        }

    } catch (error) {
        validationResults.errors.push(`Validation error: ${error.message}`)
        validationResults.isValid = false
    }

    return validationResults
}

/**
 * Generates test data for comprehensive bucket validation
 */
function generateBucketTestData() {
    const testCases = [
        {
            name: 'All Zero Calls',
            campaignLeadsData: [
                { leadid: 1, stageId: 1, reportingGroupId: 101, skillId: 201 },
                { leadid: 2, stageId: 1, reportingGroupId: 101, skillId: 201 },
                { leadid: 3, stageId: 2, reportingGroupId: 101, skillId: 201 }
            ],
            callResultsData: [], // No calls = all zero
            expectedTotalBuckets: { zero: 3, one: 0, twoToFour: 0, fiveToNineteen: 0, twentyPlus: 0 }
        },
        {
            name: 'Distributed Across All Buckets',
            campaignLeadsData: [
                { leadid: 1, stageId: 1, reportingGroupId: 101, skillId: 201 },
                { leadid: 2, stageId: 1, reportingGroupId: 101, skillId: 201 },
                { leadid: 3, stageId: 1, reportingGroupId: 101, skillId: 201 },
                { leadid: 4, stageId: 1, reportingGroupId: 101, skillId: 201 },
                { leadid: 5, stageId: 1, reportingGroupId: 101, skillId: 201 },
                { leadid: 6, stageId: 2, reportingGroupId: 101, skillId: 201 }
            ],
            callResultsData: [
                { leadid: 1, campaignStageId: 1, calls_made: 0 },   // zero
                { leadid: 2, campaignStageId: 1, calls_made: 1 },   // one
                { leadid: 3, campaignStageId: 1, calls_made: 3 },   // twoToFour
                { leadid: 4, campaignStageId: 1, calls_made: 10 },  // fiveToNineteen
                { leadid: 5, campaignStageId: 1, calls_made: 25 },  // twentyPlus
                { leadid: 6, campaignStageId: 2, calls_made: 2 }    // twoToFour
            ],
            expectedTotalBuckets: { zero: 1, one: 1, twoToFour: 2, fiveToNineteen: 1, twentyPlus: 1 }
        },
        {
            name: 'Stage Transitions',
            campaignLeadsData: [
                { leadid: 1, stageId: 2, reportingGroupId: 101, skillId: 201 }, // Moved to stage 2
                { leadid: 2, stageId: 2, reportingGroupId: 101, skillId: 201 }, // Moved to stage 2
                { leadid: 3, stageId: 1, reportingGroupId: 101, skillId: 201 }  // Still in stage 1
            ],
            callResultsData: [
                { leadid: 1, campaignStageId: 1, calls_made: 5 },   // Calls in previous stage (ignored)
                { leadid: 1, campaignStageId: 2, calls_made: 2 },   // Calls in current stage (counted)
                { leadid: 2, campaignStageId: 1, calls_made: 3 },   // Calls in previous stage (ignored)
                // Lead 2 has no calls in current stage (zero)
                { leadid: 3, campaignStageId: 1, calls_made: 1 }    // Calls in current stage (counted)
            ],
            expectedTotalBuckets: { zero: 1, one: 1, twoToFour: 1, fiveToNineteen: 0, twentyPlus: 0 }
        }
    ]

    return testCases
}

module.exports = {
    validateDialCountBuckets,
    generateBucketTestData
}
