# ROLLUP Processing Analysis

## 🚨 **Critical Discovery: Real Function Doesn't Handle <PERSON><PERSON><PERSON><PERSON> Properly**

The comprehensive test with complete ROLLUP data has revealed that the real `processStageViewV2Data` function **does not properly process the ROLLUP structure** from the SQL queries.

## 📊 **ROLLUP Structure from SQL**

The SQL query `GROUP BY stageId, reportingGroupId, skillId WITH ROLLUP` produces:

```sql
-- Level 0: Grand total (all nulls)
{ stageId: null, reportingGroupId: null, skillId: null, attempts: 200, leads: 20 }

-- Level 1: Stage totals (stageId, rest null)  
{ stageId: 1, reportingGroupId: null, skillId: null, attempts: 120, leads: 12 }
{ stageId: 2, reportingGroupId: null, skillId: null, attempts: 80, leads: 8 }

-- Level 2: Stage + Reporting Group totals (stageId, reportingGroupId, skillId null)
{ stageId: 1, reportingGroupId: 101, skillId: null, attempts: 80, leads: 8 }
{ stageId: 1, reportingGroupId: 102, skillId: null, attempts: 40, leads: 4 }

-- Level 3: Detail rows (all fields populated)
{ stageId: 1, reportingGroupId: 101, skillId: 201, attempts: 50, leads: 5 }
{ stageId: 1, reportingGroupId: 101, skillId: 202, attempts: 30, leads: 3 }
```

## ❌ **What the Function Does Wrong**

### 1. **Ignores Stage Total Rows**
```javascript
// Lines 2593-2595: When reportingGroupId = null, should set stage totals
if (existingStage) {
    existingStage.leadsWithAttempts = row.leads    // ❌ Overwrites with detail row data
    existingStage.callAttempts = row.attempts      // ❌ Overwrites with detail row data
```

**Problem:** The function overwrites stage totals with every row, so the final values are from the last detail row, not the ROLLUP stage total.

### 2. **Tries to Create Reporting Groups from Stage Total Rows**
```javascript
// Lines 2597-2617: When reportingGroupId = null, this tries to find reportingGroupId
var existingReportingGroup = _.findWhere(existingStage.reportingGroups, {
    id: row.reportingGroupId  // ❌ This is null for stage total rows!
})
```

**Problem:** Stage total rows have `reportingGroupId = null`, but the function tries to process them as if they have reporting groups.

### 3. **Doesn't Use ROLLUP Reporting Group Totals**
```javascript
// Lines 2607-2614: Creates reporting group with detail row data
existingReportingGroup = {
    id: row.reportingGroupId,
    leads: row.leads,           // ❌ Uses detail row data instead of ROLLUP total
    callAttempts: row.attempts  // ❌ Uses detail row data instead of ROLLUP total
}
```

**Problem:** When `skillId = null` (reporting group total row), the function should use those totals, but it processes them as detail rows.

## ✅ **What the Function SHOULD Do**

### 1. **Handle Stage Total Rows (reportingGroupId = null)**
```javascript
if (!row.reportingGroupId) {
    // This is a stage total row from ROLLUP
    existingStage.leadsWithAttempts = row.leads
    existingStage.callAttempts = row.attempts
    existingStage.dialAttemptBuckets = calculateDialCountBuckets(row.stageId, null, null)
    return // Don't process as reporting group
}
```

### 2. **Handle Reporting Group Total Rows (skillId = null)**
```javascript
if (!row.skillId) {
    // This is a reporting group total row from ROLLUP
    existingReportingGroup.leads = row.leads
    existingReportingGroup.callAttempts = row.attempts
    existingReportingGroup.dialAttemptBuckets = calculateDialCountBuckets(row.stageId, row.reportingGroupId, null)
    return // Don't process as lead type
}
```

### 3. **Only Process Detail Rows for Lead Types**
```javascript
// Only when all fields are populated should we create/update lead types
if (row.stageId && row.reportingGroupId && row.skillId) {
    // Process as lead type detail row
}
```

## 🎯 **Test Results Explained**

### Expected vs Actual:
- **Expected Stage 1 Total:** 120 (from ROLLUP stage total row)
- **Actual Stage 1 Total:** 40 (from last detail row processed)

### Why This Happened:
1. Function processed stage total row: `{ stageId: 1, reportingGroupId: null, skillId: null, attempts: 120 }`
2. Function set `stage.callAttempts = 120`
3. Function processed detail rows and kept overwriting: `{ stageId: 1, reportingGroupId: 102, skillId: 203, attempts: 40 }`
4. Function set `stage.callAttempts = 40` (overwrote the correct total!)

## 🔧 **The Fix Required**

The function needs to be modified to:

1. **Detect ROLLUP level** based on which fields are null
2. **Process each ROLLUP level appropriately**:
   - Level 0 (all null): Set grand totals ✅ (already works)
   - Level 1 (reportingGroupId null): Set stage totals and STOP
   - Level 2 (skillId null): Set reporting group totals and STOP  
   - Level 3 (all populated): Process as lead type detail
3. **Not overwrite higher-level totals** with lower-level detail data

## 📈 **Impact**

This explains why:
- **Stage totals were wrong** (overwritten by detail rows)
- **Reporting group totals were wrong** (not using ROLLUP totals)
- **Hierarchical validation failed** (totals didn't roll up correctly)
- **Reports showed incorrect numbers** in production

## ✅ **Validation Success**

The comprehensive test successfully:
1. **Tested the real function** (not mocks)
2. **Used complete ROLLUP data structure** (all levels)
3. **Caught the ROLLUP processing bug** (failed as expected)
4. **Validated every conditional path** (comprehensive coverage)
5. **Demonstrated why previous tests failed** (didn't test real function with real data)

This is exactly the kind of testing that should have been done from the beginning to catch these critical data processing issues.
