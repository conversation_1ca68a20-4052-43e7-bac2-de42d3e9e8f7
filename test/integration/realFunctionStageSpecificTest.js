const { expect } = require('chai')
const fs = require('fs')
const path = require('path')
const _ = require('underscore')

/**
 * Integration test for the real processStageViewV2Data function
 * This test extracts the actual function from the controller and tests it
 * with comprehensive datasets to ensure stage-specific call counting works correctly
 */

describe('Real processStageViewV2Data Function - Stage Specific Counting', function() {
    let processStageViewV2Data

    before(function() {
        // Extract the real function from the controller
        const controllerPath = path.join(__dirname, '../../controllers/campaign.js')
        const controllerCode = fs.readFileSync(controllerPath, 'utf8')
        
        // Find the function definition
        const functionStart = controllerCode.indexOf('function processStageViewV2Data(')
        if (functionStart === -1) {
            throw new Error('Could not find processStageViewV2Data function in controller')
        }
        
        // Find the end of the function (matching braces)
        let braceCount = 0
        let functionEnd = functionStart
        let inFunction = false
        
        for (let i = functionStart; i < controllerCode.length; i++) {
            if (controllerCode[i] === '{') {
                braceCount++
                inFunction = true
            } else if (controllerCode[i] === '}') {
                braceCount--
                if (inFunction && braceCount === 0) {
                    functionEnd = i + 1
                    break
                }
            }
        }
        
        // Extract the function code
        const functionCode = controllerCode.substring(functionStart, functionEnd)
        
        // Create a wrapper to execute the function
        const wrappedFunction = `
            const _ = require('underscore');
            ${functionCode}
            module.exports = processStageViewV2Data;
        `
        
        // Write to temporary file and require it
        const tempPath = path.join(__dirname, 'temp_processStageViewV2Data.js')
        fs.writeFileSync(tempPath, wrappedFunction)
        
        try {
            // Clear require cache and load the function
            delete require.cache[require.resolve('./temp_processStageViewV2Data.js')]
            processStageViewV2Data = require('./temp_processStageViewV2Data.js')
        } finally {
            // Clean up temp file
            if (fs.existsSync(tempPath)) {
                fs.unlinkSync(tempPath)
            }
        }
    })

    const mockCampaign = {
        campaignstages: [
            { id: 1, name: 'New Leads' },
            { id: 2, name: 'Follow Up' },
            { id: 3, name: 'Qualified' }
        ]
    }

    const mockSkills = [
        { id: 101, name: 'Alumni Relations' },
        { id: 102, name: 'Corporate Outreach' }
    ]

    const mockSubskills = [
        { id: 201, name: 'Recent Graduates' },
        { id: 202, name: 'Long-term Alumni' },
        { id: 203, name: 'Corporate Contacts' }
    ]

    const mockRules = []

    describe('Stage Transition Scenarios', function() {
        it('should correctly count calls only for current stage when leads move between stages', function() {
            // Comprehensive test scenario:
            // Lead 1: 2 calls in stage 1, moved to stage 2 with 3 more calls (total 5, but only 3 should count)
            // Lead 2: 4 calls in stage 1, moved to stage 2 with 0 calls (total 4, but 0 should count)
            // Lead 3: 1 call in stage 1, still in stage 1 (1 should count)
            // Lead 4: Started in stage 2 with 2 calls (2 should count)

            const callAttemptsData = [
                // ROLLUP Level 0: Grand total
                { stageId: null, reportingGroupId: null, skillId: null, attempts: 12, leads: 4 },
                
                // ROLLUP Level 1: Stage totals
                { stageId: 1, reportingGroupId: null, skillId: null, attempts: 7, leads: 1 },
                { stageId: 2, reportingGroupId: null, skillId: null, attempts: 5, leads: 3 },
                
                // ROLLUP Level 2: Reporting group totals
                { stageId: 1, reportingGroupId: 101, skillId: null, attempts: 7, leads: 1 },
                { stageId: 2, reportingGroupId: 101, skillId: null, attempts: 5, leads: 3 },
                
                // ROLLUP Level 3: Detail rows
                { stageId: 1, reportingGroupId: 101, skillId: 201, attempts: 7, leads: 1 },
                { stageId: 2, reportingGroupId: 101, skillId: 201, attempts: 5, leads: 3 }
            ]

            const callResultsData = [
                // Lead 1: calls in both stages (currently in stage 2)
                { leadid: 1, campaignStageId: 1, calls_made: 2 },
                { leadid: 1, campaignStageId: 2, calls_made: 3 },
                
                // Lead 2: calls only in stage 1 (currently in stage 2)
                { leadid: 2, campaignStageId: 1, calls_made: 4 },
                
                // Lead 3: calls only in stage 1 (currently in stage 1)
                { leadid: 3, campaignStageId: 1, calls_made: 1 },
                
                // Lead 4: calls only in stage 2 (currently in stage 2)
                { leadid: 4, campaignStageId: 2, calls_made: 2 }
            ]

            const campaignLeadsData = [
                { leadid: 1, stageId: 2, reportingGroupId: 101, skillId: 201 }, // Currently in stage 2
                { leadid: 2, stageId: 2, reportingGroupId: 101, skillId: 201 }, // Currently in stage 2
                { leadid: 3, stageId: 1, reportingGroupId: 101, skillId: 201 }, // Currently in stage 1
                { leadid: 4, stageId: 2, reportingGroupId: 101, skillId: 201 }  // Currently in stage 2
            ]

            const leadStatusData = [
                // Grand total
                { stageId: null, reportingGroupId: null, skillId: null, leads: 4, exhausted: 0, viable: 4, callback: 0, dontContactUntil: 0, noStage: 0, bad_numbers: 0 },
                
                // Stage totals
                { stageId: 1, reportingGroupId: null, skillId: null, leads: 1, exhausted: 0, viable: 1, callback: 0, dontContactUntil: 0, noStage: 0, bad_numbers: 0 },
                { stageId: 2, reportingGroupId: null, skillId: null, leads: 3, exhausted: 0, viable: 3, callback: 0, dontContactUntil: 0, noStage: 0, bad_numbers: 0 },
                
                // Reporting group totals
                { stageId: 1, reportingGroupId: 101, skillId: null, leads: 1, exhausted: 0, viable: 1, callback: 0, dontContactUntil: 0, noStage: 0, bad_numbers: 0 },
                { stageId: 2, reportingGroupId: 101, skillId: null, leads: 3, exhausted: 0, viable: 3, callback: 0, dontContactUntil: 0, noStage: 0, bad_numbers: 0 },
                
                // Detail rows
                { stageId: 1, reportingGroupId: 101, skillId: 201, leads: 1, exhausted: 0, viable: 1, callback: 0, dontContactUntil: 0, noStage: 0, bad_numbers: 0 },
                { stageId: 2, reportingGroupId: 101, skillId: 201, leads: 3, exhausted: 0, viable: 3, callback: 0, dontContactUntil: 0, noStage: 0, bad_numbers: 0 }
            ]

            const suppressionsData = []

            const result = processStageViewV2Data(
                callAttemptsData,
                callResultsData,
                campaignLeadsData,
                leadStatusData,
                suppressionsData,
                mockCampaign,
                mockSkills,
                mockSubskills,
                mockRules
            )

            // Verify the result structure
            expect(result).to.be.an('object')
            expect(result.stages).to.be.an('array')
            expect(result.stages).to.have.length(2)

            const stage1 = result.stages.find(s => s.id === 1)
            const stage2 = result.stages.find(s => s.id === 2)

            expect(stage1).to.exist
            expect(stage2).to.exist

            // Stage 1 should only count Lead 3 (1 call = one bucket)
            expect(stage1.dialAttemptBuckets.zero).to.equal(0)
            expect(stage1.dialAttemptBuckets.one).to.equal(1)
            expect(stage1.dialAttemptBuckets.twoToFour).to.equal(0)
            expect(stage1.dialAttemptBuckets.fiveToNineteen).to.equal(0)
            expect(stage1.dialAttemptBuckets.twentyPlus).to.equal(0)

            // Stage 2 should count:
            // - Lead 1: 3 calls (twoToFour bucket)
            // - Lead 2: 0 calls (zero bucket)
            // - Lead 4: 2 calls (twoToFour bucket)
            expect(stage2.dialAttemptBuckets.zero).to.equal(1)  // Lead 2
            expect(stage2.dialAttemptBuckets.one).to.equal(0)
            expect(stage2.dialAttemptBuckets.twoToFour).to.equal(2) // Lead 1 and Lead 4
            expect(stage2.dialAttemptBuckets.fiveToNineteen).to.equal(0)
            expect(stage2.dialAttemptBuckets.twentyPlus).to.equal(0)

            // Verify grand totals
            expect(result.totalDialAttemptBuckets.zero).to.equal(1)
            expect(result.totalDialAttemptBuckets.one).to.equal(1)
            expect(result.totalDialAttemptBuckets.twoToFour).to.equal(2)
            expect(result.totalDialAttemptBuckets.fiveToNineteen).to.equal(0)
            expect(result.totalDialAttemptBuckets.twentyPlus).to.equal(0)

            console.log('✓ Stage-specific call counting test passed')
            console.log('  Stage 1 buckets:', stage1.dialAttemptBuckets)
            console.log('  Stage 2 buckets:', stage2.dialAttemptBuckets)
            console.log('  Total buckets:', result.totalDialAttemptBuckets)
        })
    })

    describe('Comprehensive Bucket Validation', function() {
        const { validateDialCountBuckets, generateBucketTestData } = require('../validation/dialCountBucketValidation')

        it('should pass comprehensive bucket validation for all test cases', function() {
            const testCases = generateBucketTestData()

            testCases.forEach(testCase => {
                console.log(`\n  Testing: ${testCase.name}`)

                // Create comprehensive call attempts data with proper ROLLUP structure
                const stageIds = [...new Set(testCase.campaignLeadsData.map(lead => lead.stageId))]
                const callAttemptsData = [
                    // Grand total
                    { stageId: null, reportingGroupId: null, skillId: null, attempts: 0, leads: testCase.campaignLeadsData.length }
                ]

                // Add stage totals and detail rows
                stageIds.forEach(stageId => {
                    const stageLeads = testCase.campaignLeadsData.filter(lead => lead.stageId === stageId)
                    callAttemptsData.push(
                        // Stage total
                        { stageId: stageId, reportingGroupId: null, skillId: null, attempts: 0, leads: stageLeads.length },
                        // Reporting group total
                        { stageId: stageId, reportingGroupId: 101, skillId: null, attempts: 0, leads: stageLeads.length },
                        // Detail row
                        { stageId: stageId, reportingGroupId: 101, skillId: 201, attempts: 0, leads: stageLeads.length }
                    )
                })

                // Create comprehensive lead status data with proper ROLLUP structure
                const leadStatusData = [
                    // Grand total
                    { stageId: null, reportingGroupId: null, skillId: null, leads: testCase.campaignLeadsData.length, exhausted: 0, viable: testCase.campaignLeadsData.length, callback: 0, dontContactUntil: 0, noStage: 0, bad_numbers: 0 }
                ]

                // Add stage totals and detail rows
                stageIds.forEach(stageId => {
                    const stageLeads = testCase.campaignLeadsData.filter(lead => lead.stageId === stageId)
                    leadStatusData.push(
                        // Stage total
                        { stageId: stageId, reportingGroupId: null, skillId: null, leads: stageLeads.length, exhausted: 0, viable: stageLeads.length, callback: 0, dontContactUntil: 0, noStage: 0, bad_numbers: 0 },
                        // Reporting group total
                        { stageId: stageId, reportingGroupId: 101, skillId: null, leads: stageLeads.length, exhausted: 0, viable: stageLeads.length, callback: 0, dontContactUntil: 0, noStage: 0, bad_numbers: 0 },
                        // Detail row
                        { stageId: stageId, reportingGroupId: 101, skillId: 201, leads: stageLeads.length, exhausted: 0, viable: stageLeads.length, callback: 0, dontContactUntil: 0, noStage: 0, bad_numbers: 0 }
                    )
                })

                const result = processStageViewV2Data(
                    callAttemptsData,
                    testCase.callResultsData,
                    testCase.campaignLeadsData,
                    leadStatusData,
                    [],
                    mockCampaign,
                    mockSkills,
                    mockSubskills,
                    mockRules
                )

                const validation = validateDialCountBuckets(result, testCase.campaignLeadsData, testCase.callResultsData)

                if (!validation.isValid) {
                    console.log('    Validation errors:', validation.errors)
                    console.log('    Expected buckets:', testCase.expectedTotalBuckets)
                    console.log('    Actual buckets:', result.totalDialAttemptBuckets)
                }

                expect(validation.isValid).to.be.true
                expect(validation.errors).to.have.length(0)

                // Verify expected totals match
                Object.keys(testCase.expectedTotalBuckets).forEach(bucket => {
                    expect(result.totalDialAttemptBuckets[bucket]).to.equal(testCase.expectedTotalBuckets[bucket])
                })

                console.log(`    ✓ ${testCase.name} passed validation`)
            })
        })
    })
})
