const { expect } = require('chai')
const fs = require('fs')
const path = require('path')
const _ = require('underscore')

/**
 * Test to verify that NULL subskill filtering is consistent across all queries
 * This test addresses the issue where stage/reporting group totals were higher
 * than the sum of their lead types due to inconsistent NULL handling
 */

describe('NULL Subskill Consistency Test', function() {
    let processStageViewV2Data

    before(function() {
        // Extract the real function from the controller
        const controllerPath = path.join(__dirname, '../../controllers/campaign.js')
        const controllerCode = fs.readFileSync(controllerPath, 'utf8')
        
        // Find the function definition
        const functionStart = controllerCode.indexOf('function processStageViewV2Data(')
        if (functionStart === -1) {
            throw new Error('Could not find processStageViewV2Data function in controller')
        }
        
        // Find the end of the function (matching braces)
        let braceCount = 0
        let functionEnd = functionStart
        let inFunction = false
        
        for (let i = functionStart; i < controllerCode.length; i++) {
            if (controllerCode[i] === '{') {
                braceCount++
                inFunction = true
            } else if (controllerCode[i] === '}') {
                braceCount--
                if (inFunction && braceCount === 0) {
                    functionEnd = i + 1
                    break
                }
            }
        }
        
        // Extract the function code
        const functionCode = controllerCode.substring(functionStart, functionEnd)
        
        // Create a wrapper to execute the function
        const wrappedFunction = `
            const _ = require('underscore');
            ${functionCode}
            module.exports = processStageViewV2Data;
        `
        
        // Write to temporary file and require it
        const tempPath = path.join(__dirname, 'temp_nullSubskillTest.js')
        fs.writeFileSync(tempPath, wrappedFunction)
        
        try {
            // Clear require cache and load the function
            delete require.cache[require.resolve('./temp_nullSubskillTest.js')]
            processStageViewV2Data = require('./temp_nullSubskillTest.js')
        } finally {
            // Clean up temp file
            if (fs.existsSync(tempPath)) {
                fs.unlinkSync(tempPath)
            }
        }
    })

    const mockCampaign = {
        campaignstages: [
            { id: 1, name: 'New Leads' },
            { id: 2, name: 'Follow Up' }
        ]
    }

    const mockSkills = [
        { id: 101, name: 'Alumni Relations' },
        { id: 102, name: 'Corporate Outreach' }
    ]

    const mockSubskills = [
        { id: 201, name: 'Recent Graduates' },
        { id: 202, name: 'Long-term Alumni' }
    ]

    const mockRules = []

    describe('Consistent NULL Subskill Handling', function() {
        it('should have consistent totals when all queries filter NULL subskills', function() {
            // Test scenario: Some leads have NULL subskills, but all queries should filter them out consistently
            // This simulates the fix where all queries now include "AND l.subskillid IS NOT NULL"

            const callAttemptsData = [
                // Grand total - should only include leads with non-NULL subskills
                { stageId: null, reportingGroupId: null, skillId: null, attempts: 8, leads: 4 },
                
                // Stage totals - should only include leads with non-NULL subskills
                { stageId: 1, reportingGroupId: null, skillId: null, attempts: 5, leads: 2 },
                { stageId: 2, reportingGroupId: null, skillId: null, attempts: 3, leads: 2 },
                
                // Reporting group totals - should only include leads with non-NULL subskills
                { stageId: 1, reportingGroupId: 101, skillId: null, attempts: 5, leads: 2 },
                { stageId: 2, reportingGroupId: 101, skillId: null, attempts: 3, leads: 2 },
                
                // Detail rows - only leads with non-NULL subskills
                { stageId: 1, reportingGroupId: 101, skillId: 201, attempts: 3, leads: 1 },
                { stageId: 1, reportingGroupId: 101, skillId: 202, attempts: 2, leads: 1 },
                { stageId: 2, reportingGroupId: 101, skillId: 201, attempts: 1, leads: 1 },
                { stageId: 2, reportingGroupId: 101, skillId: 202, attempts: 2, leads: 1 }
            ]

            const callResultsData = [
                // Only leads with non-NULL subskills (filtered by query)
                { leadid: 1, campaignStageId: 1, calls_made: 3 },
                { leadid: 2, campaignStageId: 1, calls_made: 2 },
                { leadid: 3, campaignStageId: 2, calls_made: 1 },
                { leadid: 4, campaignStageId: 2, calls_made: 2 }
            ]

            const campaignLeadsData = [
                // Only leads with non-NULL subskills (filtered by query)
                { leadid: 1, stageId: 1, reportingGroupId: 101, skillId: 201 },
                { leadid: 2, stageId: 1, reportingGroupId: 101, skillId: 202 },
                { leadid: 3, stageId: 2, reportingGroupId: 101, skillId: 201 },
                { leadid: 4, stageId: 2, reportingGroupId: 101, skillId: 202 }
            ]

            const leadStatusData = [
                // Grand total - only leads with non-NULL subskills
                { stageId: null, reportingGroupId: null, skillId: null, leads: 4, exhausted: 0, viable: 4, callback: 0, dontContactUntil: 0, noStage: 0, bad_numbers: 0 },
                
                // Stage totals - only leads with non-NULL subskills
                { stageId: 1, reportingGroupId: null, skillId: null, leads: 2, exhausted: 0, viable: 2, callback: 0, dontContactUntil: 0, noStage: 0, bad_numbers: 0 },
                { stageId: 2, reportingGroupId: null, skillId: null, leads: 2, exhausted: 0, viable: 2, callback: 0, dontContactUntil: 0, noStage: 0, bad_numbers: 0 },
                
                // Reporting group totals - only leads with non-NULL subskills
                { stageId: 1, reportingGroupId: 101, skillId: null, leads: 2, exhausted: 0, viable: 2, callback: 0, dontContactUntil: 0, noStage: 0, bad_numbers: 0 },
                { stageId: 2, reportingGroupId: 101, skillId: null, leads: 2, exhausted: 0, viable: 2, callback: 0, dontContactUntil: 0, noStage: 0, bad_numbers: 0 },
                
                // Detail rows - only leads with non-NULL subskills
                { stageId: 1, reportingGroupId: 101, skillId: 201, leads: 1, exhausted: 0, viable: 1, callback: 0, dontContactUntil: 0, noStage: 0, bad_numbers: 0 },
                { stageId: 1, reportingGroupId: 101, skillId: 202, leads: 1, exhausted: 0, viable: 1, callback: 0, dontContactUntil: 0, noStage: 0, bad_numbers: 0 },
                { stageId: 2, reportingGroupId: 101, skillId: 201, leads: 1, exhausted: 0, viable: 1, callback: 0, dontContactUntil: 0, noStage: 0, bad_numbers: 0 },
                { stageId: 2, reportingGroupId: 101, skillId: 202, leads: 1, exhausted: 0, viable: 1, callback: 0, dontContactUntil: 0, noStage: 0, bad_numbers: 0 }
            ]

            const suppressionsData = []

            const result = processStageViewV2Data(
                callAttemptsData,
                callResultsData,
                campaignLeadsData,
                leadStatusData,
                suppressionsData,
                mockCampaign,
                mockSkills,
                mockSubskills,
                mockRules
            )

            // Verify the result structure
            expect(result).to.be.an('object')
            expect(result.stages).to.be.an('array')
            expect(result.stages).to.have.length(2)

            const stage1 = result.stages.find(s => s.id === 1)
            const stage2 = result.stages.find(s => s.id === 2)

            expect(stage1).to.exist
            expect(stage2).to.exist

            // Critical test: Stage totals should equal sum of lead type totals
            const stage1ReportingGroup = stage1.reportingGroups.find(rg => rg.id === 101)
            const stage2ReportingGroup = stage2.reportingGroups.find(rg => rg.id === 101)

            expect(stage1ReportingGroup).to.exist
            expect(stage2ReportingGroup).to.exist

            // Stage 1: Should have 2 leads total, split between 2 lead types
            expect(stage1.leads).to.equal(2)
            expect(stage1ReportingGroup.leads).to.equal(2)
            
            const stage1LeadTypeTotal = stage1ReportingGroup.leadTypes.reduce((sum, lt) => sum + lt.leads, 0)
            expect(stage1LeadTypeTotal).to.equal(2, 'Stage 1 lead type sum should equal reporting group total')
            expect(stage1LeadTypeTotal).to.equal(stage1.leads, 'Stage 1 lead type sum should equal stage total')

            // Stage 2: Should have 2 leads total, split between 2 lead types
            expect(stage2.leads).to.equal(2)
            expect(stage2ReportingGroup.leads).to.equal(2)
            
            const stage2LeadTypeTotal = stage2ReportingGroup.leadTypes.reduce((sum, lt) => sum + lt.leads, 0)
            expect(stage2LeadTypeTotal).to.equal(2, 'Stage 2 lead type sum should equal reporting group total')
            expect(stage2LeadTypeTotal).to.equal(stage2.leads, 'Stage 2 lead type sum should equal stage total')

            // Verify dial count buckets are also consistent (Node 6 compatible)
            const stage1BucketTotal = Object.keys(stage1.dialAttemptBuckets).reduce((sum, key) => sum + stage1.dialAttemptBuckets[key], 0)
            const stage1RGBucketTotal = Object.keys(stage1ReportingGroup.dialAttemptBuckets).reduce((sum, key) => sum + stage1ReportingGroup.dialAttemptBuckets[key], 0)
            const stage1LTBucketTotal = stage1ReportingGroup.leadTypes.reduce((sum, lt) =>
                sum + Object.keys(lt.dialAttemptBuckets).reduce((ltSum, key) => ltSum + lt.dialAttemptBuckets[key], 0), 0)

            expect(stage1BucketTotal).to.equal(stage1RGBucketTotal, 'Stage 1 bucket totals should match')
            expect(stage1RGBucketTotal).to.equal(stage1LTBucketTotal, 'Stage 1 reporting group bucket total should equal sum of lead type buckets')

            console.log('✓ NULL subskill consistency test passed')
            console.log('  Stage 1 totals: Stage=' + stage1.leads + ', RG=' + stage1ReportingGroup.leads + ', LT Sum=' + stage1LeadTypeTotal)
            console.log('  Stage 2 totals: Stage=' + stage2.leads + ', RG=' + stage2ReportingGroup.leads + ', LT Sum=' + stage2LeadTypeTotal)
        })
    })
})
