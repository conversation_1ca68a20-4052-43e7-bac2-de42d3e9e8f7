const { expect } = require('chai')
const request = require('supertest')
const _ = require('underscore')

// Mock database setup for integration testing
const mockDatabase = {
    // Mock call attempts data
    callAttempts: [
        { id: 1, leadid: 1, campaignid: 1, createdfromdtuuid: 'rule1' },
        { id: 2, leadid: 2, campaignid: 1, createdfromdtuuid: 'rule1' },
        { id: 3, leadid: 3, campaignid: 1, createdfromdtuuid: 'rule2' },
        { id: 4, leadid: 4, campaignid: 1, createdfromdtuuid: 'rule2' },
        { id: 5, leadid: 5, campaignid: 1, createdfromdtuuid: 'rule1' }
    ],

    // Mock call results data
    callResults: [
        { id: 1, leadid: 1, campaignid: 1, createdAt: '2025-01-01' },
        { id: 2, leadid: 2, campaignid: 1, createdAt: '2025-01-01' },
        { id: 3, leadid: 2, campaignid: 1, createdAt: '2025-01-02' }, // Lead 2 has 2 calls
        { id: 4, leadid: 3, campaignid: 1, createdAt: '2025-01-01' },
        { id: 5, leadid: 3, campaignid: 1, createdAt: '2025-01-02' },
        { id: 6, leadid: 3, campaignid: 1, createdAt: '2025-01-03' }, // Lead 3 has 3 calls
        // Lead 4 has no call results (0 calls)
        // Lead 5 has no call results (0 calls)
    ],

    // Mock campaign leads data
    campaignLeads: [
        { leadid: 1, campaignid: 1, currentCampaignStageId: 1 },
        { leadid: 2, campaignid: 1, currentCampaignStageId: 1 },
        { leadid: 3, campaignid: 1, currentCampaignStageId: 2 },
        { leadid: 4, campaignid: 1, currentCampaignStageId: 2 },
        { leadid: 5, campaignid: 1, currentCampaignStageId: 1 }
    ],

    // Mock leads data
    leads: [
        { id: 1, tfSkillId: 101, tfSubskillid: 201 },
        { id: 2, tfSkillId: 101, tfSubskillid: 201 },
        { id: 3, tfSkillId: 102, tfSubskillid: 202 },
        { id: 4, tfSkillId: 102, tfSubskillid: 202 },
        { id: 5, tfSkillId: 101, tfSubskillid: 201 }
    ]
}

// Mock query functions that simulate the actual database queries
function mockQuery1_CallAttempts(campaignId) {
    // Simulate Query 1: Call attempts analysis
    const results = []
    
    // Group by stage, reportingGroup, skill
    const groups = {}
    
    mockDatabase.callAttempts
        .filter(ca => ca.campaignid === campaignId)
        .forEach(ca => {
            const cl = mockDatabase.campaignLeads.find(cl => cl.leadid === ca.leadid)
            const lead = mockDatabase.leads.find(l => l.id === ca.leadid)
            
            if (cl && lead) {
                const key = `${cl.currentCampaignStageId}-${lead.tfSkillId}-${lead.tfSubskillid}`
                if (!groups[key]) {
                    groups[key] = {
                        stageId: cl.currentCampaignStageId,
                        reportingGroupId: lead.tfSkillId,
                        skillId: lead.tfSubskillid,
                        ruleId: ca.createdfromdtuuid,
                        attempts: 0,
                        leads: new Set()
                    }
                }
                groups[key].attempts++
                groups[key].leads.add(ca.leadid)
            }
        })
    
    // Convert to array format
    Object.keys(groups).forEach(key => {
        const group = groups[key]
        results.push({
            stageId: group.stageId,
            reportingGroupId: group.reportingGroupId,
            skillId: group.skillId,
            ruleId: group.ruleId,
            attempts: group.attempts,
            leads: group.leads.size
        })
    })
    
    // Add total row (WITH ROLLUP simulation)
    const totalAttempts = mockDatabase.callAttempts.filter(ca => ca.campaignid === campaignId).length
    const totalLeads = new Set(mockDatabase.callAttempts.filter(ca => ca.campaignid === campaignId).map(ca => ca.leadid)).size
    
    results.unshift({
        stageId: null,
        reportingGroupId: null,
        skillId: null,
        ruleId: null,
        attempts: totalAttempts,
        leads: totalLeads
    })
    
    return results
}

function mockQuery2_CallResults(campaignId) {
    // Simulate Query 2: Call results count per lead
    const results = {}
    
    mockDatabase.callResults
        .filter(cr => cr.campaignid === campaignId)
        .forEach(cr => {
            if (!results[cr.leadid]) {
                results[cr.leadid] = { leadid: cr.leadid, calls_made: 0 }
            }
            results[cr.leadid].calls_made++
        })
    
    return Object.keys(results).map(key => results[key])
}

function mockQuery3_CampaignLeads(campaignId) {
    // Simulate Query 3: Campaign leads with stage/skill info
    return mockDatabase.campaignLeads
        .filter(cl => cl.campaignid === campaignId && cl.currentCampaignStageId)
        .map(cl => {
            const lead = mockDatabase.leads.find(l => l.id === cl.leadid)
            return {
                leadid: cl.leadid,
                stageId: cl.currentCampaignStageId,
                reportingGroupId: lead.tfSkillId,
                skillId: lead.tfSubskillid
            }
        })
}

describe('CallAnalysisV2 Integration Tests', function() {
    describe('Query Result Validation', function() {
        it('should produce consistent totals across all three queries', function() {
            const campaignId = 1
            
            // Execute the three queries
            const query1Results = mockQuery1_CallAttempts(campaignId)
            const query2Results = mockQuery2_CallResults(campaignId)
            const query3Results = mockQuery3_CampaignLeads(campaignId)
            
            console.log('Query 1 Results (Call Attempts):', JSON.stringify(query1Results, null, 2))
            console.log('Query 2 Results (Call Results):', JSON.stringify(query2Results, null, 2))
            console.log('Query 3 Results (Campaign Leads):', JSON.stringify(query3Results, null, 2))
            
            // Validate Query 1 totals
            const totalRow = query1Results.find(r => r.stageId === null)
            expect(totalRow).to.exist
            expect(totalRow.attempts).to.equal(5) // 5 call attempts total
            expect(totalRow.leads).to.equal(5)    // 5 unique leads with attempts
            
            // Validate Query 2 call results count
            const totalCallResults = query2Results.reduce((sum, r) => sum + r.calls_made, 0)
            expect(totalCallResults).to.equal(6) // 6 total call results
            
            // Validate Query 3 campaign leads count
            expect(query3Results).to.have.length(5) // 5 campaign leads total
            
            // Cross-validate: All leads in query3 should be in the campaign
            query3Results.forEach(lead => {
                const campaignLead = mockDatabase.campaignLeads.find(cl => cl.leadid === lead.leadid)
                expect(campaignLead).to.exist
                expect(campaignLead.campaignid).to.equal(campaignId)
            })
        })

        it('should correctly map call results to campaign leads', function() {
            const campaignId = 1
            
            const query2Results = mockQuery2_CallResults(campaignId)
            const query3Results = mockQuery3_CampaignLeads(campaignId)
            
            // Create lookup map like the actual code does
            const callResultsMap = {}
            query2Results.forEach(row => {
                callResultsMap[row.leadid] = row.calls_made || 0
            })
            
            // Validate dial count bucket distribution
            const dialCounts = { zero: 0, one: 0, twoToFour: 0, fiveToNineteen: 0, twentyPlus: 0 }
            
            query3Results.forEach(lead => {
                const callsMade = callResultsMap[lead.leadid] || 0
                
                if (callsMade === 0) dialCounts.zero++
                else if (callsMade === 1) dialCounts.one++
                else if (callsMade >= 2 && callsMade <= 4) dialCounts.twoToFour++
                else if (callsMade >= 5 && callsMade <= 19) dialCounts.fiveToNineteen++
                else if (callsMade >= 20) dialCounts.twentyPlus++
            })
            
            console.log('Dial Count Distribution:', dialCounts)
            
            // Expected distribution based on mock data:
            // Lead 1: 1 call (one bucket)
            // Lead 2: 2 calls (twoToFour bucket)  
            // Lead 3: 3 calls (twoToFour bucket)
            // Lead 4: 0 calls (zero bucket)
            // Lead 5: 0 calls (zero bucket)
            
            expect(dialCounts.zero).to.equal(2)      // Leads 4, 5
            expect(dialCounts.one).to.equal(1)       // Lead 1
            expect(dialCounts.twoToFour).to.equal(2) // Leads 2, 3
            expect(dialCounts.fiveToNineteen).to.equal(0)
            expect(dialCounts.twentyPlus).to.equal(0)
            
            // Total should equal number of campaign leads
            const totalBuckets = Object.keys(dialCounts).reduce((sum, key) => sum + dialCounts[key], 0)
            expect(totalBuckets).to.equal(query3Results.length)
        })

        it('should correctly group data by stage and skill combinations', function() {
            const campaignId = 1
            
            const query1Results = mockQuery1_CallAttempts(campaignId)
            const query3Results = mockQuery3_CampaignLeads(campaignId)
            
            // Remove total row for grouping analysis
            const detailRows = query1Results.filter(r => r.stageId !== null)
            
            // Validate stage groupings
            const stageGroups = _.groupBy(detailRows, 'stageId')
            const stage1Rows = stageGroups[1] || []
            const stage2Rows = stageGroups[2] || []
            
            console.log('Stage 1 Rows:', stage1Rows)
            console.log('Stage 2 Rows:', stage2Rows)
            
            // Stage 1 should have leads 1, 2, 5 (3 leads)
            const stage1LeadCount = stage1Rows.reduce((sum, row) => sum + row.leads, 0)
            expect(stage1LeadCount).to.be.greaterThan(0)
            
            // Stage 2 should have leads 3, 4 (2 leads)  
            const stage2LeadCount = stage2Rows.reduce((sum, row) => sum + row.leads, 0)
            expect(stage2LeadCount).to.be.greaterThan(0)
            
            // Total detail leads should match campaign leads
            const totalDetailLeads = detailRows.reduce((sum, row) => sum + row.leads, 0)
            expect(totalDetailLeads).to.equal(query3Results.length)
        })
    })

    describe('Data Consistency Validation', function() {
        it('should maintain referential integrity across queries', function() {
            const campaignId = 1
            
            const query1Results = mockQuery1_CallAttempts(campaignId)
            const query2Results = mockQuery2_CallResults(campaignId)
            const query3Results = mockQuery3_CampaignLeads(campaignId)
            
            // All leads in query2 should exist in query3
            query2Results.forEach(callResult => {
                const campaignLead = query3Results.find(cl => cl.leadid === callResult.leadid)
                expect(campaignLead).to.exist, `Lead ${callResult.leadid} has call results but is not in campaign leads`
            })
            
            // All leads in query1 detail rows should exist in query3
            const detailRows = query1Results.filter(r => r.stageId !== null)
            // Note: This is harder to validate directly since query1 is aggregated
            // But we can check that the total leads match
            const totalRow = query1Results.find(r => r.stageId === null)
            expect(totalRow.leads).to.equal(query3Results.length)
        })

        it('should handle edge cases correctly', function() {
            // Test with leads that have no call results
            const leadsWithoutCalls = mockDatabase.campaignLeads
                .filter(cl => !mockDatabase.callResults.some(cr => cr.leadid === cl.leadid))
            
            expect(leadsWithoutCalls).to.have.length.greaterThan(0), 'Should have some leads without call results for testing'
            
            // Test with leads that have multiple call results
            const leadsWithMultipleCalls = mockDatabase.callResults
                .reduce((acc, cr) => {
                    acc[cr.leadid] = (acc[cr.leadid] || 0) + 1
                    return acc
                }, {})
            
            const multipleCallLeads = Object.keys(leadsWithMultipleCalls)
                .filter(leadid => leadsWithMultipleCalls[leadid] > 1)
            
            expect(multipleCallLeads).to.have.length.greaterThan(0), 'Should have some leads with multiple calls for testing'
        })
    })

    describe('Performance and Scale Testing', function() {
        it('should handle large datasets efficiently', function() {
            // Generate larger mock dataset for performance testing
            const largeCampaignId = 2
            const numLeads = 1000

            // Generate mock data
            const largeMockData = {
                callAttempts: [],
                callResults: [],
                campaignLeads: [],
                leads: []
            }

            for (let i = 1; i <= numLeads; i++) {
                // Create leads
                largeMockData.leads.push({
                    id: i,
                    tfSkillId: 101 + (i % 3), // Distribute across 3 skills
                    tfSubskillid: 201 + (i % 5) // Distribute across 5 subskills
                })

                // Create campaign leads
                largeMockData.campaignLeads.push({
                    leadid: i,
                    campaignid: largeCampaignId,
                    currentCampaignStageId: 1 + (i % 3) // Distribute across 3 stages
                })

                // Create call attempts (some leads have multiple)
                const numAttempts = 1 + (i % 3)
                for (let j = 0; j < numAttempts; j++) {
                    largeMockData.callAttempts.push({
                        id: i * 10 + j,
                        leadid: i,
                        campaignid: largeCampaignId,
                        createdfromdtuuid: `rule${1 + (i % 2)}`
                    })
                }

                // Create call results (70% of leads have call results)
                if (i % 10 < 7) {
                    const numCalls = Math.floor(Math.random() * 5) + 1 // 1-5 calls
                    for (let k = 0; k < numCalls; k++) {
                        largeMockData.callResults.push({
                            id: i * 100 + k,
                            leadid: i,
                            campaignid: largeCampaignId,
                            createdAt: `2025-01-${(1 + k < 10 ? '0' : '') + (1 + k)}`
                        })
                    }
                }
            }

            // Mock the queries with large dataset
            function mockLargeQuery2(campaignId) {
                const results = {}
                largeMockData.callResults
                    .filter(cr => cr.campaignid === campaignId)
                    .forEach(cr => {
                        if (!results[cr.leadid]) {
                            results[cr.leadid] = { leadid: cr.leadid, calls_made: 0 }
                        }
                        results[cr.leadid].calls_made++
                    })
                return Object.keys(results).map(key => results[key])
            }

            function mockLargeQuery3(campaignId) {
                return largeMockData.campaignLeads
                    .filter(cl => cl.campaignid === campaignId && cl.currentCampaignStageId)
                    .map(cl => {
                        const lead = largeMockData.leads.find(l => l.id === cl.leadid)
                        return {
                            leadid: cl.leadid,
                            stageId: cl.currentCampaignStageId,
                            reportingGroupId: lead.tfSkillId,
                            skillId: lead.tfSubskillid
                        }
                    })
            }

            // Time the operations
            const startTime = Date.now()

            const query2Results = mockLargeQuery2(largeCampaignId)
            const query3Results = mockLargeQuery3(largeCampaignId)

            // Create lookup map (this is the key optimization)
            const callResultsMap = {}
            query2Results.forEach(row => {
                callResultsMap[row.leadid] = row.calls_made || 0
            })

            // Calculate dial count buckets
            const dialCounts = { zero: 0, one: 0, twoToFour: 0, fiveToNineteen: 0, twentyPlus: 0 }
            query3Results.forEach(lead => {
                const callsMade = callResultsMap[lead.leadid] || 0

                if (callsMade === 0) dialCounts.zero++
                else if (callsMade === 1) dialCounts.one++
                else if (callsMade >= 2 && callsMade <= 4) dialCounts.twoToFour++
                else if (callsMade >= 5 && callsMade <= 19) dialCounts.fiveToNineteen++
                else if (callsMade >= 20) dialCounts.twentyPlus++
            })

            const endTime = Date.now()
            const processingTime = endTime - startTime

            console.log(`Processed ${numLeads} leads in ${processingTime}ms`)
            console.log('Large Dataset Dial Count Distribution:', dialCounts)

            // Validate results
            expect(query3Results).to.have.length(numLeads)

            const totalBuckets = Object.keys(dialCounts).reduce((sum, key) => sum + dialCounts[key], 0)
            expect(totalBuckets).to.equal(numLeads)

            // Performance assertion - should process 1000 leads quickly
            expect(processingTime).to.be.lessThan(100, 'Should process 1000 leads in under 100ms')
        })
    })
})
