const { expect } = require('chai')
const _ = require('underscore')

// Mock the processStageViewV2Data function for testing
function processStageViewV2Data(callAttemptsData, callResultsData, campaignLeadsData, leadStatusData, suppressionsData, campaign, skills, rules) {
    var stages = []
    var totalAttempts = 0
    var totalLeads = 0
    var totalExhausted = 0
    var totalLeadsWithAttempts = 0
    var totalViable = 0
    var totalCallback = 0
    var totalDontContactUntil = 0
    var totalNoStage = 0
    var totalBadNumbers = 0
    var totalDialAttemptBuckets = {
        zero: 0,
        one: 0,
        twoToFour: 0,
        fiveToNineteen: 0,
        twentyPlus: 0
    }

    var lastSkill
    var lastRule

    // Create lookup map
    var callResultsMap = {}
    callResultsData.forEach(row => {
        callResultsMap[row.leadid] = row.calls_made || 0
    })

    // Helper function to calculate dial count buckets from separated data
    function calculateDialCountBuckets(stageId, reportingGroupId, skillId) {
        var buckets = { zero: 0, one: 0, twoToFour: 0, fiveToNineteen: 0, twentyPlus: 0 }

        campaignLeadsData.forEach(lead => {
            // Match the grouping criteria
            var matchesStage = !stageId || lead.stageId === stageId
            var matchesReportingGroup = !reportingGroupId || lead.reportingGroupId === reportingGroupId
            var matchesSkill = !skillId || lead.skillId === skillId

            if (matchesStage && matchesReportingGroup && matchesSkill) {
                var callsMade = callResultsMap[lead.leadid] || 0

                if (callsMade === 0) buckets.zero++
                else if (callsMade === 1) buckets.one++
                else if (callsMade >= 2 && callsMade <= 4) buckets.twoToFour++
                else if (callsMade >= 5 && callsMade <= 19) buckets.fiveToNineteen++
                else if (callsMade >= 20) buckets.twentyPlus++
            }
        })

        return buckets
    }

    // Process call attempts data
    callAttemptsData.forEach(row => {
        try {
            if (!row.stageId) {
                totalAttempts = row.attempts
                totalLeadsWithAttempts = row.leads
                // Calculate total dial count buckets from separated data
                totalDialAttemptBuckets = calculateDialCountBuckets(null, null, null)
                return
            }

            var stage = _.findWhere(campaign.campaignstages, {
                id: row.stageId
            })

            var skill = _.findWhere(skills, {
                id: row.skillId
            })

            var existingStage = _.findWhere(stages, {
                id: row.stageId
            })

            if (existingStage) {
                existingStage.leadsWithAttempts = row.leads
                existingStage.callAttempts = row.attempts

                var existingReportingGroup = _.findWhere(existingStage.reportingGroups, {
                    id: row.reportingGroupId
                })

                if (!existingReportingGroup) {
                    // Find reporting group info - reportingGroupId is the skill ID
                    var reportingGroupInfo = _.findWhere(skills, {
                        id: row.reportingGroupId
                    })

                    existingReportingGroup = {
                        id: row.reportingGroupId,
                        name: reportingGroupInfo ? reportingGroupInfo.name : 'Unknown Group',
                        leads: row.leads,
                        callAttempts: row.attempts,
                        dialAttemptBuckets: calculateDialCountBuckets(row.stageId, row.reportingGroupId, null),
                        leadTypes: []
                    }
                    if (!existingStage.reportingGroups) existingStage.reportingGroups = []
                    existingStage.reportingGroups.push(existingReportingGroup)
                }

                var existingLeadType = _.findWhere(existingReportingGroup.leadTypes, {
                    id: row.skillId
                })

                if (!existingLeadType) {
                    // skillId is the subskill ID - find the subskill info
                    var reportingGroupInfo = _.findWhere(skills, { id: row.reportingGroupId })
                    var subskillInfo = null
                    if (reportingGroupInfo && reportingGroupInfo.subskills) {
                        subskillInfo = _.findWhere(reportingGroupInfo.subskills, {
                            id: row.skillId
                        })
                    }

                    existingLeadType = {
                        id: row.skillId,
                        name: subskillInfo ? subskillInfo.name : (skill ? skill.name : 'Unknown Lead Type'),
                        callAttempts: row.attempts,
                        leadsWithAttempts: row.leads,
                        dialAttemptBuckets: calculateDialCountBuckets(row.stageId, row.reportingGroupId, row.skillId)
                    }
                    existingReportingGroup.leadTypes.push(existingLeadType)
                } else if (!row.ruleId && lastRule !== null) {
                    existingLeadType.callAttempts = row.attempts
                    existingLeadType.leadsWithAttempts = row.leads
                }

                // Dialing rule data is aggregated at lead type level, no separate dialing rules segmentation needed
            } else {
                // Create new stage
                var newstage = {
                    id: row.stageId,
                    name: stage ? stage.name : '',
                    leadsWithAttempts: row.leads,
                    callAttempts: row.attempts,
                    dialAttemptBuckets: calculateDialCountBuckets(row.stageId, null, null),
                    reportingGroups: []
                }

                // Add reporting group and lead type
                if (row.reportingGroupId && row.skillId) {
                    // reportingGroupId is the skill ID
                    var reportingGroupInfo = _.findWhere(skills, {
                        id: row.reportingGroupId
                    })

                    // skillId is the subskill ID
                    var subskillInfo = null
                    if (reportingGroupInfo && reportingGroupInfo.subskills) {
                        subskillInfo = _.findWhere(reportingGroupInfo.subskills, {
                            id: row.skillId
                        })
                    }

                    var newReportingGroup = {
                        id: row.reportingGroupId,
                        name: reportingGroupInfo ? reportingGroupInfo.name : 'Unknown Group',
                        leads: row.leads,
                        callAttempts: row.attempts,
                        dialAttemptBuckets: calculateDialCountBuckets(row.stageId, row.reportingGroupId, null),
                        leadTypes: [{
                            id: row.skillId,
                            name: subskillInfo ? subskillInfo.name : (skill ? skill.name : 'Unknown Lead Type'),
                            callAttempts: row.attempts,
                            leadsWithAttempts: row.leads,
                            dialAttemptBuckets: calculateDialCountBuckets(row.stageId, row.reportingGroupId, row.skillId)
                        }]
                    }
                    newstage.reportingGroups.push(newReportingGroup)

                    // Dialing rule data aggregated at lead type level, no separate segmentation needed
                }
                stages.push(newstage)
            }

            lastSkill = row.skillId
            lastRule = row.ruleId
        } catch (err) {
            console.log(row)
            console.log(err)
        }
    })

    return {
        stages: stages,
        totalAttempts: totalAttempts,
        totalCallAttempts: totalAttempts,  // Frontend expects this field name
        totalLeads: totalLeads,
        totalExhausted: totalExhausted,
        totalNoCallAttempts: totalExhausted,  // Frontend expects this field name
        totalLeadsWithAttempts: totalLeadsWithAttempts,
        totalViable: totalViable,
        totalCallback: totalCallback,
        totalDontContactUntil: totalDontContactUntil,
        totalNoStage: totalNoStage,
        totalBadNumbers: totalBadNumbers,
        suppressedTotal: 0,
        totalDialAttemptBuckets: totalDialAttemptBuckets
    }
}

describe('Reporting Group and Lead Type Resolution Tests', function() {
    it('should correctly resolve reporting group and lead type names', function() {
        // Mock data with proper skill/subskill structure
        const skills = [
            {
                id: 101,
                name: 'Telefunding Skill A',
                subskills: [
                    { id: 201, name: 'Donor Type 1' },
                    { id: 202, name: 'Donor Type 2' }
                ]
            },
            {
                id: 102,
                name: 'Telefunding Skill B',
                subskills: [
                    { id: 203, name: 'Donor Type 3' },
                    { id: 204, name: 'Donor Type 4' }
                ]
            }
        ]

        const campaign = {
            campaignstages: [
                { id: 1, name: 'Initial Contact' },
                { id: 2, name: 'Follow Up' }
            ]
        }

        const callAttemptsData = [
            { stageId: null, reportingGroupId: null, skillId: null, ruleId: null, attempts: 10, leads: 4 },
            { stageId: 1, reportingGroupId: 101, skillId: 201, ruleId: 'rule1', attempts: 5, leads: 2 },
            { stageId: 1, reportingGroupId: 101, skillId: 202, ruleId: 'rule1', attempts: 3, leads: 1 },
            { stageId: 2, reportingGroupId: 102, skillId: 203, ruleId: 'rule2', attempts: 2, leads: 1 }
        ]

        const callResultsData = [
            { leadid: 1, calls_made: 2 },
            { leadid: 2, calls_made: 1 },
            { leadid: 3, calls_made: 3 },
            { leadid: 4, calls_made: 0 }
        ]

        const campaignLeadsData = [
            { leadid: 1, stageId: 1, reportingGroupId: 101, skillId: 201 },
            { leadid: 2, stageId: 1, reportingGroupId: 101, skillId: 202 },
            { leadid: 3, stageId: 1, reportingGroupId: 101, skillId: 201 },
            { leadid: 4, stageId: 2, reportingGroupId: 102, skillId: 203 }
        ]

        const result = processStageViewV2Data(
            callAttemptsData,
            callResultsData,
            campaignLeadsData,
            [], // leadStatusData
            [], // suppressionsData
            campaign,
            skills,
            [] // rules
        )

        // Validate stage structure
        expect(result.stages).to.have.length(2)

        // Validate Stage 1
        const stage1 = result.stages.find(s => s.id === 1)
        expect(stage1).to.exist
        expect(stage1.name).to.equal('Initial Contact')
        expect(stage1.reportingGroups).to.have.length(1)

        // Validate Reporting Group (Skill A)
        const reportingGroup1 = stage1.reportingGroups[0]
        expect(reportingGroup1.id).to.equal(101)
        expect(reportingGroup1.name).to.equal('Telefunding Skill A')
        expect(reportingGroup1.leadTypes).to.have.length(2)

        // Validate Lead Types (Subskills)
        const leadType1 = reportingGroup1.leadTypes.find(lt => lt.id === 201)
        expect(leadType1).to.exist
        expect(leadType1.name).to.equal('Donor Type 1')

        const leadType2 = reportingGroup1.leadTypes.find(lt => lt.id === 202)
        expect(leadType2).to.exist
        expect(leadType2.name).to.equal('Donor Type 2')

        // Validate Stage 2
        const stage2 = result.stages.find(s => s.id === 2)
        expect(stage2).to.exist
        expect(stage2.name).to.equal('Follow Up')
        expect(stage2.reportingGroups).to.have.length(1)

        // Validate Reporting Group (Skill B)
        const reportingGroup2 = stage2.reportingGroups[0]
        expect(reportingGroup2.id).to.equal(102)
        expect(reportingGroup2.name).to.equal('Telefunding Skill B')
        expect(reportingGroup2.leadTypes).to.have.length(1)

        // Validate Lead Type (Subskill)
        const leadType3 = reportingGroup2.leadTypes[0]
        expect(leadType3.id).to.equal(203)
        expect(leadType3.name).to.equal('Donor Type 3')

        console.log('✅ All reporting groups and lead types resolved correctly!')
        console.log('Stage 1 - Reporting Group:', reportingGroup1.name)
        console.log('Stage 1 - Lead Types:', reportingGroup1.leadTypes.map(lt => lt.name))
        console.log('Stage 2 - Reporting Group:', reportingGroup2.name)
        console.log('Stage 2 - Lead Types:', reportingGroup2.leadTypes.map(lt => lt.name))
    })

    it('should handle missing subskill information gracefully', function() {
        // Test with skills that don't have subskills defined
        const skills = [
            {
                id: 101,
                name: 'Telefunding Skill A'
                // No subskills array
            }
        ]

        const campaign = {
            campaignstages: [
                { id: 1, name: 'Initial Contact' }
            ]
        }

        const callAttemptsData = [
            { stageId: 1, reportingGroupId: 101, skillId: 201, ruleId: 'rule1', attempts: 5, leads: 2 }
        ]

        const result = processStageViewV2Data(
            callAttemptsData,
            [],
            [],
            [],
            [],
            campaign,
            skills,
            []
        )

        const stage1 = result.stages.find(s => s.id === 1)
        const reportingGroup1 = stage1.reportingGroups[0]
        const leadType1 = reportingGroup1.leadTypes[0]

        expect(reportingGroup1.name).to.equal('Telefunding Skill A')
        expect(leadType1.name).to.equal('Unknown Lead Type') // Should fallback gracefully

        console.log('✅ Missing subskill handled gracefully:', leadType1.name)
    })
})
