const { expect } = require('chai')
const fs = require('fs')
const path = require('path')

// This is how we SHOULD test - by importing the actual function
// Since the function is embedded in a large controller file, we need to extract it

function extractAndTestRealFunction() {
    // Read the actual controller file
    const controllerPath = path.join(__dirname, '../../controllers/campaign.js')
    const controllerCode = fs.readFileSync(controllerPath, 'utf8')
    
    // Find the processStageViewV2Data function
    const functionStart = controllerCode.indexOf('function processStageViewV2Data(')
    if (functionStart === -1) {
        throw new Error('Could not find processStageViewV2Data function in controller')
    }
    
    // Extract the function (this is a simplified approach)
    // In a real scenario, you'd refactor to make the function importable
    let braceCount = 0
    let functionEnd = functionStart
    let inFunction = false
    
    for (let i = functionStart; i < controllerCode.length; i++) {
        const char = controllerCode[i]
        if (char === '{') {
            braceCount++
            inFunction = true
        } else if (char === '}') {
            braceCount--
            if (inFunction && braceCount === 0) {
                functionEnd = i + 1
                break
            }
        }
    }
    
    const functionCode = controllerCode.substring(functionStart, functionEnd)
    
    // Create a test environment with required dependencies
    const _ = require('underscore')
    
    // Evaluate the function in our test context
    const testFunction = eval(`(${functionCode})`)
    
    return testFunction
}

describe('Real Function Testing', function() {
    let processStageViewV2Data
    
    before(function() {
        try {
            processStageViewV2Data = extractAndTestRealFunction()
            console.log('✅ Successfully extracted real function from controller')
        } catch (error) {
            console.log('❌ Could not extract function:', error.message)
            console.log('This demonstrates why the tests were not testing the real function!')
            
            // Skip the tests if we can't extract the function
            this.skip()
        }
    })
    
    it('should test the ACTUAL function with real data structure', function() {
        // Test data that matches the real production structure
        const skills = [
            {
                id: 101,
                name: 'Telefunding Skill A',
                subskills: [
                    { id: 201, name: 'Donor Type 1' },
                    { id: 202, name: 'Donor Type 2' }
                ]
            },
            {
                id: 102,
                name: 'Telefunding Skill B',
                subskills: [
                    { id: 203, name: 'Donor Type 3' }
                ]
            },
            {
                id: 103,
                name: 'Telefunding Skill C',
                subskills: [
                    { id: 204, name: 'Donor Type 4' }
                ]
            }
        ]

        const campaign = {
            campaignstages: [
                { id: 1, name: 'Initial Contact' },
                { id: 2, name: 'Follow Up' }
            ]
        }

        // Call attempts data with COMPLETE ROLLUP structure
        // GROUP BY stageId, reportingGroupId, skillId, ruleId WITH ROLLUP produces:
        const callAttemptsData = [
            // 1. GRAND TOTAL (all nulls) - ROLLUP level 0
            { stageId: null, reportingGroupId: null, skillId: null, ruleId: null, attempts: 200, leads: 20 },

            // 2. STAGE TOTALS (stageId, rest null) - ROLLUP level 1
            { stageId: 1, reportingGroupId: null, skillId: null, ruleId: null, attempts: 120, leads: 12 },
            { stageId: 2, reportingGroupId: null, skillId: null, ruleId: null, attempts: 80, leads: 8 },

            // 3. STAGE + REPORTING GROUP TOTALS (stageId, reportingGroupId, rest null) - ROLLUP level 2
            { stageId: 1, reportingGroupId: 101, skillId: null, ruleId: null, attempts: 80, leads: 8 },
            { stageId: 1, reportingGroupId: 102, skillId: null, ruleId: null, attempts: 40, leads: 4 },
            { stageId: 2, reportingGroupId: 101, skillId: null, ruleId: null, attempts: 50, leads: 5 },
            { stageId: 2, reportingGroupId: 103, skillId: null, ruleId: null, attempts: 30, leads: 3 },

            // 4. STAGE + REPORTING GROUP + SKILL TOTALS (stageId, reportingGroupId, skillId, ruleId null) - ROLLUP level 3
            { stageId: 1, reportingGroupId: 101, skillId: 201, ruleId: null, attempts: 50, leads: 5 },
            { stageId: 1, reportingGroupId: 101, skillId: 202, ruleId: null, attempts: 30, leads: 3 },
            { stageId: 1, reportingGroupId: 102, skillId: 203, ruleId: null, attempts: 40, leads: 4 },
            { stageId: 2, reportingGroupId: 101, skillId: 201, ruleId: null, attempts: 50, leads: 5 },
            { stageId: 2, reportingGroupId: 103, skillId: 204, ruleId: null, attempts: 30, leads: 3 },

            // 5. DETAIL ROWS (all fields populated) - ROLLUP level 4 (actual data)
            { stageId: 1, reportingGroupId: 101, skillId: 201, ruleId: 'rule1', attempts: 30, leads: 3 },
            { stageId: 1, reportingGroupId: 101, skillId: 201, ruleId: 'rule2', attempts: 20, leads: 2 },
            { stageId: 1, reportingGroupId: 101, skillId: 202, ruleId: 'rule1', attempts: 30, leads: 3 },
            { stageId: 1, reportingGroupId: 102, skillId: 203, ruleId: 'rule3', attempts: 40, leads: 4 },
            { stageId: 2, reportingGroupId: 101, skillId: 201, ruleId: 'rule1', attempts: 25, leads: 2 },
            { stageId: 2, reportingGroupId: 101, skillId: 201, ruleId: 'rule2', attempts: 25, leads: 3 },
            { stageId: 2, reportingGroupId: 103, skillId: 204, ruleId: 'rule4', attempts: 30, leads: 3 }
        ]

        // Call results data covering all dial count buckets
        const callResultsData = [
            // Stage 1, Skill A (101), Lead Type 1 (201) - 5 leads
            { leadid: 1, calls_made: 0 },   // zero bucket
            { leadid: 2, calls_made: 1 },   // one bucket
            { leadid: 3, calls_made: 3 },   // twoToFour bucket
            { leadid: 4, calls_made: 7 },   // fiveToNineteen bucket
            { leadid: 5, calls_made: 25 },  // twentyPlus bucket

            // Stage 1, Skill A (101), Lead Type 2 (202) - 3 leads
            { leadid: 6, calls_made: 0 },   // zero bucket
            { leadid: 7, calls_made: 1 },   // one bucket
            { leadid: 8, calls_made: 2 },   // twoToFour bucket

            // Stage 1, Skill B (102), Lead Type 3 (203) - 4 leads
            { leadid: 9, calls_made: 0 },   // zero bucket
            { leadid: 10, calls_made: 1 },  // one bucket
            { leadid: 11, calls_made: 4 },  // twoToFour bucket
            { leadid: 12, calls_made: 15 }, // fiveToNineteen bucket

            // Stage 2, Skill A (101), Lead Type 1 (201) - 5 leads
            { leadid: 13, calls_made: 0 },  // zero bucket
            { leadid: 14, calls_made: 1 },  // one bucket
            { leadid: 15, calls_made: 2 },  // twoToFour bucket
            { leadid: 16, calls_made: 3 },  // twoToFour bucket
            { leadid: 17, calls_made: 8 },  // fiveToNineteen bucket

            // Stage 2, Skill C (103), Lead Type 4 (204) - 3 leads
            { leadid: 18, calls_made: 0 },  // zero bucket
            { leadid: 19, calls_made: 1 },  // one bucket
            { leadid: 20, calls_made: 22 }  // twentyPlus bucket
        ]

        // Campaign leads data matching the call attempts structure
        const campaignLeadsData = [
            // Stage 1, Skill A (101), Lead Type 1 (201) - 5 leads
            { leadid: 1, stageId: 1, reportingGroupId: 101, skillId: 201 },
            { leadid: 2, stageId: 1, reportingGroupId: 101, skillId: 201 },
            { leadid: 3, stageId: 1, reportingGroupId: 101, skillId: 201 },
            { leadid: 4, stageId: 1, reportingGroupId: 101, skillId: 201 },
            { leadid: 5, stageId: 1, reportingGroupId: 101, skillId: 201 },

            // Stage 1, Skill A (101), Lead Type 2 (202) - 3 leads
            { leadid: 6, stageId: 1, reportingGroupId: 101, skillId: 202 },
            { leadid: 7, stageId: 1, reportingGroupId: 101, skillId: 202 },
            { leadid: 8, stageId: 1, reportingGroupId: 101, skillId: 202 },

            // Stage 1, Skill B (102), Lead Type 3 (203) - 4 leads
            { leadid: 9, stageId: 1, reportingGroupId: 102, skillId: 203 },
            { leadid: 10, stageId: 1, reportingGroupId: 102, skillId: 203 },
            { leadid: 11, stageId: 1, reportingGroupId: 102, skillId: 203 },
            { leadid: 12, stageId: 1, reportingGroupId: 102, skillId: 203 },

            // Stage 2, Skill A (101), Lead Type 1 (201) - 5 leads
            { leadid: 13, stageId: 2, reportingGroupId: 101, skillId: 201 },
            { leadid: 14, stageId: 2, reportingGroupId: 101, skillId: 201 },
            { leadid: 15, stageId: 2, reportingGroupId: 101, skillId: 201 },
            { leadid: 16, stageId: 2, reportingGroupId: 101, skillId: 201 },
            { leadid: 17, stageId: 2, reportingGroupId: 101, skillId: 201 },

            // Stage 2, Skill C (103), Lead Type 4 (204) - 3 leads
            { leadid: 18, stageId: 2, reportingGroupId: 103, skillId: 204 },
            { leadid: 19, stageId: 2, reportingGroupId: 103, skillId: 204 },
            { leadid: 20, stageId: 2, reportingGroupId: 103, skillId: 204 }
        ]

        console.log('\n=== TESTING REAL FUNCTION WITH COMPLETE ROLLUP DATA ===')

        // Separate skills and subskills for the new function signature
        const skillsOnly = [
            { id: 101, name: 'Telefunding Skill A' },
            { id: 102, name: 'Telefunding Skill B' },
            { id: 103, name: 'Telefunding Skill C' }
        ]

        const subskillsOnly = [
            { id: 201, name: 'Donor Type 1' },
            { id: 202, name: 'Donor Type 2' },
            { id: 203, name: 'Donor Type 3' },
            { id: 204, name: 'Donor Type 4' }
        ]

        // Call the ACTUAL function from the controller
        const result = processStageViewV2Data(
            callAttemptsData,
            callResultsData,
            campaignLeadsData,
            [], // leadStatusData
            [], // suppressionsData
            campaign,
            skillsOnly,
            subskillsOnly,
            [] // rules
        )

        console.log('Result stages:', result.stages.length)
        console.log('Total call attempts:', result.totalCallAttempts)
        console.log('Total leads with attempts:', result.totalLeadsWithAttempts)

        // CRITICAL TEST 1: Validate ROLLUP grand total processing (stageId = null)
        expect(result.totalCallAttempts).to.equal(200, 'Grand total should match ROLLUP total row')
        expect(result.totalLeadsWithAttempts).to.equal(20, 'Grand total leads should match ROLLUP total row')

        // CRITICAL TEST 2: Validate stage-level aggregation
        expect(result.stages).to.have.length(2, 'Should have 2 stages')

        const stage1 = result.stages.find(s => s.id === 1)
        const stage2 = result.stages.find(s => s.id === 2)

        expect(stage1).to.exist
        expect(stage2).to.exist

        // Validate stage totals match ROLLUP stage totals
        expect(stage1.callAttempts).to.equal(120, 'Stage 1 total should match ROLLUP stage total')
        expect(stage1.leadsWithAttempts).to.equal(12, 'Stage 1 leads should match ROLLUP stage total')
        expect(stage2.callAttempts).to.equal(80, 'Stage 2 total should match ROLLUP stage total')
        expect(stage2.leadsWithAttempts).to.equal(8, 'Stage 2 leads should match ROLLUP stage total')

        // CRITICAL TEST 3: Validate reporting group aggregation
        console.log('\n--- Stage 1 Reporting Groups ---')
        expect(stage1.reportingGroups).to.have.length(2, 'Stage 1 should have 2 reporting groups')

        const stage1SkillA = stage1.reportingGroups.find(rg => rg.id === 101)
        const stage1SkillB = stage1.reportingGroups.find(rg => rg.id === 102)

        expect(stage1SkillA).to.exist
        expect(stage1SkillB).to.exist

        // Test name resolution (the original bug)
        expect(stage1SkillA.name).to.equal('Telefunding Skill A', 'Should resolve skill name correctly')
        expect(stage1SkillB.name).to.equal('Telefunding Skill B', 'Should resolve skill name correctly')

        // Validate reporting group totals match ROLLUP reporting group totals
        expect(stage1SkillA.callAttempts).to.equal(80, 'Stage 1 Skill A total should match ROLLUP')
        expect(stage1SkillA.leads).to.equal(8, 'Stage 1 Skill A leads should match ROLLUP')
        expect(stage1SkillB.callAttempts).to.equal(40, 'Stage 1 Skill B total should match ROLLUP')
        expect(stage1SkillB.leads).to.equal(4, 'Stage 1 Skill B leads should match ROLLUP')

        // CRITICAL TEST 4: Validate lead type aggregation
        console.log('\n--- Lead Types ---')
        console.log('Stage 1 Skill A lead types:', stage1SkillA.leadTypes.length)
        stage1SkillA.leadTypes.forEach(lt => {
            console.log(`  Lead Type ${lt.id}: ${lt.name}, attempts: ${lt.callAttempts}, leads: ${lt.leadsWithAttempts}`)
        })

        expect(stage1SkillA.leadTypes).to.have.length(2, 'Skill A should have 2 lead types')

        const leadType1 = stage1SkillA.leadTypes.find(lt => lt.id === 201)
        const leadType2 = stage1SkillA.leadTypes.find(lt => lt.id === 202)

        expect(leadType1).to.exist
        expect(leadType2).to.exist

        // Test lead type name resolution (the original bug)
        expect(leadType1.name).to.equal('Donor Type 1', 'Should resolve lead type name correctly')
        expect(leadType2.name).to.equal('Donor Type 2', 'Should resolve lead type name correctly')

        // Validate lead type totals match ROLLUP lead type totals
        expect(leadType1.callAttempts).to.equal(50, 'Lead Type 1 total should match ROLLUP')
        expect(leadType1.leadsWithAttempts).to.equal(5, 'Lead Type 1 leads should match ROLLUP')
        expect(leadType2.callAttempts).to.equal(30, 'Lead Type 2 total should match ROLLUP')
        expect(leadType2.leadsWithAttempts).to.equal(3, 'Lead Type 2 leads should match ROLLUP')

        // CRITICAL TEST 5: Validate dial count buckets at all levels
        console.log('\n--- Dial Count Buckets ---')

        // Expected buckets for Stage 1, Skill A, Lead Type 1 (leads 1-5)
        // Lead 1: 0 calls (zero), Lead 2: 1 call (one), Lead 3: 3 calls (twoToFour),
        // Lead 4: 7 calls (fiveToNineteen), Lead 5: 25 calls (twentyPlus)
        const expectedLeadType1Buckets = { zero: 1, one: 1, twoToFour: 1, fiveToNineteen: 1, twentyPlus: 1 }

        expect(leadType1.dialAttemptBuckets).to.deep.equal(expectedLeadType1Buckets,
            'Lead Type 1 dial buckets should match expected distribution')

        // CRITICAL TEST 6: Validate hierarchical total consistency
        console.log('\n--- Hierarchical Total Validation ---')

        // Stage total should equal sum of reporting group totals
        const stage1ReportingGroupSum = stage1.reportingGroups.reduce((sum, rg) => sum + rg.callAttempts, 0)
        expect(stage1.callAttempts).to.equal(stage1ReportingGroupSum,
            'Stage 1 total should equal sum of reporting group totals')

        // Reporting group total should equal sum of lead type totals
        const skillALeadTypeSum = stage1SkillA.leadTypes.reduce((sum, lt) => sum + lt.callAttempts, 0)
        expect(stage1SkillA.callAttempts).to.equal(skillALeadTypeSum,
            'Skill A total should equal sum of lead type totals')

        // Grand total should equal sum of stage totals
        const grandTotalFromStages = result.stages.reduce((sum, stage) => sum + stage.callAttempts, 0)
        expect(result.totalCallAttempts).to.equal(grandTotalFromStages,
            'Grand total should equal sum of stage totals')

        console.log('✅ ALL ROLLUP AND HIERARCHICAL VALIDATIONS PASSED!')
        console.log('✅ Real function test completed with comprehensive validation')
    })
    
    it('should test every conditional path in processStageViewV2Data', function() {
        console.log('\n=== TESTING ALL CONDITIONAL PATHS ===')

        // Test data designed to hit every conditional branch
        const testSkills = [
            { id: 101, name: 'Test Skill' },
            { id: 999, name: 'Missing Subskills' }
        ]

        const testSubskills = [
            { id: 201, name: 'Test Lead Type' }
            // Note: subskill 999 is intentionally missing to test "Unknown Lead Type" case
        ]

        const testCampaign = {
            campaignstages: [{ id: 1, name: 'Test Stage' }]
        }

        // Test ROLLUP data with edge cases
        const testCallAttemptsData = [
            // 1. GRAND TOTAL ROW (stageId = null) - tests line 2573-2578
            { stageId: null, reportingGroupId: null, skillId: null, ruleId: null, attempts: 100, leads: 10 },

            // 2. STAGE TOTAL ROW (reportingGroupId = null) - tests line 2581-2590
            { stageId: 1, reportingGroupId: null, skillId: null, ruleId: null, attempts: 100, leads: 10 },

            // 3. REPORTING GROUP TOTAL ROW (skillId = null) - tests line 2593-2610
            { stageId: 1, reportingGroupId: 101, skillId: null, ruleId: null, attempts: 50, leads: 5 },
            { stageId: 1, reportingGroupId: 999, skillId: null, ruleId: null, attempts: 50, leads: 5 }, // Missing skill

            // 4. DETAIL ROWS (all fields populated) - tests line 2613+
            { stageId: 1, reportingGroupId: 101, skillId: 201, ruleId: 'rule1', attempts: 25, leads: 2 },
            { stageId: 1, reportingGroupId: 101, skillId: 999, ruleId: 'rule1', attempts: 25, leads: 3 }, // Missing subskill
            { stageId: 1, reportingGroupId: 999, skillId: 201, ruleId: 'rule1', attempts: 25, leads: 2 }, // Missing skill entirely
            { stageId: 1, reportingGroupId: 999, skillId: 999, ruleId: 'rule1', attempts: 25, leads: 3 }  // Both missing
        ]

        const testCallResultsData = [
            { leadid: 1, calls_made: 0 },
            { leadid: 2, calls_made: 1 },
            { leadid: 3, calls_made: 5 },
            { leadid: 4, calls_made: 25 }
        ]

        const testCampaignLeadsData = [
            { leadid: 1, stageId: 1, reportingGroupId: 101, skillId: 201 },
            { leadid: 2, stageId: 1, reportingGroupId: 101, skillId: 201 },
            { leadid: 3, stageId: 1, reportingGroupId: 101, skillId: 999 },
            { leadid: 4, stageId: 1, reportingGroupId: 999, skillId: 201 }
        ]

        const result = processStageViewV2Data(
            testCallAttemptsData,
            testCallResultsData,
            testCampaignLeadsData,
            [], [], testCampaign, testSkills, testSubskills, []
        )

        console.log('Testing conditional paths...')

        // TEST PATH 1: Grand total processing (stageId = null)
        expect(result.totalCallAttempts).to.equal(100, 'Should process grand total row')
        expect(result.totalLeadsWithAttempts).to.equal(10, 'Should process grand total leads')

        // TEST PATH 2: Stage creation and totals
        expect(result.stages).to.have.length(1, 'Should create stage from stage total row')
        const stage = result.stages[0]
        expect(stage.id).to.equal(1)
        expect(stage.name).to.equal('Test Stage')
        expect(stage.callAttempts).to.equal(100, 'Should set stage totals from stage total row')

        // TEST PATH 3: Reporting group creation with valid skill
        const validReportingGroup = stage.reportingGroups.find(rg => rg.id === 101)
        expect(validReportingGroup).to.exist
        expect(validReportingGroup.name).to.equal('Test Skill', 'Should resolve valid skill name')

        // TEST PATH 4: Reporting group creation with skill that has no subskills
        const skillWithoutSubskills = stage.reportingGroups.find(rg => rg.id === 999)
        expect(skillWithoutSubskills).to.exist
        expect(skillWithoutSubskills.name).to.equal('Missing Subskills', 'Should resolve skill name even without subskills')

        // TEST PATH 5: Lead type creation with valid subskill
        const validLeadType = validReportingGroup.leadTypes.find(lt => lt.id === 201)
        expect(validLeadType).to.exist
        expect(validLeadType.name).to.equal('Test Lead Type', 'Should resolve valid subskill name')

        // TEST PATH 6: Lead type creation with missing subskill
        const missingLeadType = validReportingGroup.leadTypes.find(lt => lt.id === 999)
        expect(missingLeadType).to.exist
        expect(missingLeadType.name).to.equal('Unknown Lead Type', 'Should handle missing subskill gracefully')

        // TEST PATH 7: Dial count bucket calculation
        expect(validLeadType.dialAttemptBuckets).to.exist
        expect(validLeadType.dialAttemptBuckets.zero).to.be.a('number')
        expect(validLeadType.dialAttemptBuckets.one).to.be.a('number')
        expect(validLeadType.dialAttemptBuckets.twoToFour).to.be.a('number')
        expect(validLeadType.dialAttemptBuckets.fiveToNineteen).to.be.a('number')
        expect(validLeadType.dialAttemptBuckets.twentyPlus).to.be.a('number')

        console.log('✅ All conditional paths tested successfully!')
    })

    it('should demonstrate the testing methodology problem', function() {
        console.log('\n=== TESTING METHODOLOGY ANALYSIS ===')
        console.log('❌ Previous tests used MOCK functions instead of real function')
        console.log('❌ Mock functions were written correctly, so they passed')
        console.log('❌ Real function with bugs was never tested')
        console.log('❌ This gave false confidence in the code quality')
        console.log('')
        console.log('✅ Proper testing should:')
        console.log('  1. Import/extract the actual function from the controller')
        console.log('  2. Test with real data structures (skills with subskills)')
        console.log('  3. Validate business logic, not just technical structure')
        console.log('  4. Test hierarchical aggregation at every level')
        console.log('  5. Validate field mapping and name resolution')
        console.log('')
        console.log('🎯 This is why the bugs went undetected!')
    })
})

describe('Why Mock Testing Failed', function() {
    it('should explain the fundamental testing flaw', function() {
        console.log('\n=== THE FUNDAMENTAL TESTING FLAW ===')
        console.log('')
        console.log('🚨 WHAT ACTUALLY HAPPENED:')
        console.log('  1. Tests created MOCK versions of processStageViewV2Data')
        console.log('  2. Mock versions were written correctly (by me)')
        console.log('  3. Tests passed because mocks worked correctly')
        console.log('  4. REAL function in controllers/campaign.js was never tested')
        console.log('  5. Real function had critical bugs that went undetected')
        console.log('')
        console.log('🎯 THE LESSON:')
        console.log('  - Mock testing is useful for dependencies, not the code under test')
        console.log('  - Always test the ACTUAL function/endpoint/component')
        console.log('  - Integration tests should call real code paths')
        console.log('  - Unit tests should import and test real functions')
        console.log('')
        console.log('✅ CORRECT APPROACH:')
        console.log('  1. Refactor function to be importable (export from module)')
        console.log('  2. Import real function in tests')
        console.log('  3. Test with production-like data structures')
        console.log('  4. Validate business logic and edge cases')
        console.log('  5. Mock only external dependencies (database, APIs)')
        
        // This test always passes - it's just for explanation
        expect(true).to.be.true
    })
})
