const { expect } = require('chai')
const fs = require('fs')
const path = require('path')
const _ = require('underscore')

/**
 * Test to verify that dial bucket totals are consistent between
 * stage/reporting group levels and the sum of their lead types
 * 
 * This test addresses the issue where leads are included in stage totals
 * but excluded from dial bucket calculations due to different filtering criteria
 */

describe('Dial Bucket Consistency Test', function() {
    let processStageViewV2Data

    before(function() {
        // Extract the real function from the controller
        const controllerPath = path.join(__dirname, '../../controllers/campaign.js')
        const controllerCode = fs.readFileSync(controllerPath, 'utf8')
        
        // Find the function definition
        const functionStart = controllerCode.indexOf('function processStageViewV2Data(')
        if (functionStart === -1) {
            throw new Error('Could not find processStageViewV2Data function in controller')
        }
        
        // Find the end of the function (matching braces)
        let braceCount = 0
        let functionEnd = functionStart
        let inFunction = false
        
        for (let i = functionStart; i < controllerCode.length; i++) {
            if (controllerCode[i] === '{') {
                braceCount++
                inFunction = true
            } else if (controllerCode[i] === '}') {
                braceCount--
                if (inFunction && braceCount === 0) {
                    functionEnd = i + 1
                    break
                }
            }
        }
        
        // Extract the function code
        const functionCode = controllerCode.substring(functionStart, functionEnd)
        
        // Create a wrapper to execute the function
        const wrappedFunction = `
            const _ = require('underscore');
            ${functionCode}
            module.exports = processStageViewV2Data;
        `
        
        // Write to temporary file and require it
        const tempPath = path.join(__dirname, 'temp_dialBucketTest.js')
        fs.writeFileSync(tempPath, wrappedFunction)
        
        try {
            // Clear require cache and load the function
            delete require.cache[require.resolve('./temp_dialBucketTest.js')]
            processStageViewV2Data = require('./temp_dialBucketTest.js')
        } finally {
            // Clean up temp file
            if (fs.existsSync(tempPath)) {
                fs.unlinkSync(tempPath)
            }
        }
    })

    const mockCampaign = {
        campaignstages: [
            { id: 1, name: 'New Leads' },
            { id: 2, name: 'Follow Up' }
        ]
    }

    const mockSkills = [
        { id: 101, name: 'Alumni Relations' },
        { id: 102, name: 'Corporate Outreach' }
    ]

    const mockSubskills = [
        { id: 201, name: 'Recent Graduates' },
        { id: 202, name: 'Long-term Alumni' }
    ]

    const mockRules = []

    describe('Consistent Filtering Across Queries', function() {
        it('should have consistent dial bucket totals when queries use same filtering criteria', function() {
            // Test scenario: Some leads are excluded from dial bucket calculations due to:
            // - Bad phone numbers
            // - Active callbacks
            // - Active suppressions
            // - No call attempts
            // These leads should also be excluded from stage/reporting group totals

            const callAttemptsData = [
                // Grand total - should only include leads that meet all filtering criteria
                { stageId: null, reportingGroupId: null, skillId: null, attempts: 6, leads: 3 },
                
                // Stage totals - should only include leads that meet all filtering criteria
                { stageId: 1, reportingGroupId: null, skillId: null, attempts: 4, leads: 2 },
                { stageId: 2, reportingGroupId: null, skillId: null, attempts: 2, leads: 1 },
                
                // Reporting group totals - should only include leads that meet all filtering criteria
                { stageId: 1, reportingGroupId: 101, skillId: null, attempts: 4, leads: 2 },
                { stageId: 2, reportingGroupId: 101, skillId: null, attempts: 2, leads: 1 },
                
                // Detail rows - only leads that meet all filtering criteria
                { stageId: 1, reportingGroupId: 101, skillId: 201, attempts: 2, leads: 1 },
                { stageId: 1, reportingGroupId: 101, skillId: 202, attempts: 2, leads: 1 },
                { stageId: 2, reportingGroupId: 101, skillId: 201, attempts: 2, leads: 1 }
            ]

            const callResultsData = [
                // Only leads that meet all filtering criteria (valid phones, no callbacks/suppressions, have call attempts)
                { leadid: 1, campaignStageId: 1, calls_made: 2 },
                { leadid: 2, campaignStageId: 1, calls_made: 1 },
                { leadid: 3, campaignStageId: 2, calls_made: 3 }
            ]

            const campaignLeadsData = [
                // Only leads that meet all filtering criteria (same as call results)
                { leadid: 1, stageId: 1, reportingGroupId: 101, skillId: 201 },
                { leadid: 2, stageId: 1, reportingGroupId: 101, skillId: 202 },
                { leadid: 3, stageId: 2, reportingGroupId: 101, skillId: 201 }
                // Note: Leads 4, 5, 6 are excluded due to bad phones/callbacks/suppressions/no attempts
            ]

            const leadStatusData = [
                // Grand total - only leads that meet all filtering criteria
                { stageId: null, reportingGroupId: null, skillId: null, leads: 3, exhausted: 0, viable: 3, callback: 0, dontContactUntil: 0, noStage: 0, bad_numbers: 0 },
                
                // Stage totals - only leads that meet all filtering criteria
                { stageId: 1, reportingGroupId: null, skillId: null, leads: 2, exhausted: 0, viable: 2, callback: 0, dontContactUntil: 0, noStage: 0, bad_numbers: 0 },
                { stageId: 2, reportingGroupId: null, skillId: null, leads: 1, exhausted: 0, viable: 1, callback: 0, dontContactUntil: 0, noStage: 0, bad_numbers: 0 },
                
                // Reporting group totals - only leads that meet all filtering criteria
                { stageId: 1, reportingGroupId: 101, skillId: null, leads: 2, exhausted: 0, viable: 2, callback: 0, dontContactUntil: 0, noStage: 0, bad_numbers: 0 },
                { stageId: 2, reportingGroupId: 101, skillId: null, leads: 1, exhausted: 0, viable: 1, callback: 0, dontContactUntil: 0, noStage: 0, bad_numbers: 0 },
                
                // Detail rows - only leads that meet all filtering criteria
                { stageId: 1, reportingGroupId: 101, skillId: 201, leads: 1, exhausted: 0, viable: 1, callback: 0, dontContactUntil: 0, noStage: 0, bad_numbers: 0 },
                { stageId: 1, reportingGroupId: 101, skillId: 202, leads: 1, exhausted: 0, viable: 1, callback: 0, dontContactUntil: 0, noStage: 0, bad_numbers: 0 },
                { stageId: 2, reportingGroupId: 101, skillId: 201, leads: 1, exhausted: 0, viable: 1, callback: 0, dontContactUntil: 0, noStage: 0, bad_numbers: 0 }
            ]

            const suppressionsData = []

            const result = processStageViewV2Data(
                callAttemptsData,
                callResultsData,
                campaignLeadsData,
                leadStatusData,
                suppressionsData,
                mockCampaign,
                mockSkills,
                mockSubskills,
                mockRules
            )

            // Verify the result structure
            expect(result).to.be.an('object')
            expect(result.stages).to.be.an('array')
            expect(result.stages).to.have.length(2)

            const stage1 = result.stages.find(s => s.id === 1)
            const stage2 = result.stages.find(s => s.id === 2)

            expect(stage1).to.exist
            expect(stage2).to.exist

            // Critical test: Dial bucket totals should be consistent across all levels
            const stage1ReportingGroup = stage1.reportingGroups.find(rg => rg.id === 101)
            const stage2ReportingGroup = stage2.reportingGroups.find(rg => rg.id === 101)

            expect(stage1ReportingGroup).to.exist
            expect(stage2ReportingGroup).to.exist

            // Stage 1: Dial bucket consistency
            const stage1StageBucketTotal = Object.keys(stage1.dialAttemptBuckets).reduce((sum, key) => sum + stage1.dialAttemptBuckets[key], 0)
            const stage1RGBucketTotal = Object.keys(stage1ReportingGroup.dialAttemptBuckets).reduce((sum, key) => sum + stage1ReportingGroup.dialAttemptBuckets[key], 0)
            const stage1LTBucketTotal = stage1ReportingGroup.leadTypes.reduce((sum, lt) => 
                sum + Object.keys(lt.dialAttemptBuckets).reduce((ltSum, key) => ltSum + lt.dialAttemptBuckets[key], 0), 0)

            expect(stage1StageBucketTotal).to.equal(stage1RGBucketTotal, 'Stage 1: Stage bucket total should equal reporting group bucket total')
            expect(stage1RGBucketTotal).to.equal(stage1LTBucketTotal, 'Stage 1: Reporting group bucket total should equal sum of lead type buckets')

            // Stage 2: Dial bucket consistency
            const stage2StageBucketTotal = Object.keys(stage2.dialAttemptBuckets).reduce((sum, key) => sum + stage2.dialAttemptBuckets[key], 0)
            const stage2RGBucketTotal = Object.keys(stage2ReportingGroup.dialAttemptBuckets).reduce((sum, key) => sum + stage2ReportingGroup.dialAttemptBuckets[key], 0)
            const stage2LTBucketTotal = stage2ReportingGroup.leadTypes.reduce((sum, lt) => 
                sum + Object.keys(lt.dialAttemptBuckets).reduce((ltSum, key) => ltSum + lt.dialAttemptBuckets[key], 0), 0)

            expect(stage2StageBucketTotal).to.equal(stage2RGBucketTotal, 'Stage 2: Stage bucket total should equal reporting group bucket total')
            expect(stage2RGBucketTotal).to.equal(stage2LTBucketTotal, 'Stage 2: Reporting group bucket total should equal sum of lead type buckets')

            // Grand total consistency
            const grandTotalBucketTotal = Object.keys(result.totalDialAttemptBuckets).reduce((sum, key) => sum + result.totalDialAttemptBuckets[key], 0)
            const stagesBucketTotal = stage1StageBucketTotal + stage2StageBucketTotal

            expect(grandTotalBucketTotal).to.equal(stagesBucketTotal, 'Grand total bucket total should equal sum of stage bucket totals')

            console.log('✓ Dial bucket consistency test passed')
            console.log('  Stage 1 bucket totals: Stage=' + stage1StageBucketTotal + ', RG=' + stage1RGBucketTotal + ', LT Sum=' + stage1LTBucketTotal)
            console.log('  Stage 2 bucket totals: Stage=' + stage2StageBucketTotal + ', RG=' + stage2RGBucketTotal + ', LT Sum=' + stage2LTBucketTotal)
            console.log('  Grand total: ' + grandTotalBucketTotal + ', Stages sum: ' + stagesBucketTotal)
        })
    })
})
