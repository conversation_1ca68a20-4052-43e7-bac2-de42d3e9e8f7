# docker-compose.yml
services:
  mysql:
    image: mysql:5.7
    ports:
          - "3306:3306"
    environment:
          - MYSQL_ROOT_PASSWORD=root
          - MYSQL_DATABASE=dialer
          - MYSQL_USER=api
          - MYSQL_PASSWORD=secret
    volumes:
      - mysql-data:/var/lib/mysql
  redis:
    image: redis:3
    restart: always
    ports:
      - '6379:6379'
    volumes: 
      - redis-data:/var/lib/data
volumes:
  mysql-data:
  redis-data: