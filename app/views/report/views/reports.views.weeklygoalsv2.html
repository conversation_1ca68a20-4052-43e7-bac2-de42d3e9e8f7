<div class="ibox">
	<div class="ibox-content form-inline">
		<div class="form-group">
			<label class="control-label" style="margin-right: 10px;">Week:</label>
			<select ng-model="selectedWeek" class="form-control" ng-options="week as week.name for week in availableWeeks" ng-change="getResults()" style="width: 200px; margin-right: 20px;"></select>
		</div>
	</div>
	<div class="ibox-content">
		<!-- Enhanced table with sticky headers and natural styling -->
		<style>
			.weekly-goals-table {
				width: 100%;
				border-collapse: separate;
				border-spacing: 0;
				font-size: 13px;
				background-color: white;
			}

			.weekly-goals-table thead th {
				background-color: #f8f9fa;
				border-bottom: 2px solid #e9ecef;
				padding: 12px 8px;
				text-align: center;
				font-weight: bold;
				position: sticky;
				top: 0;
				z-index: 10;
				box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
			}

			.weekly-goals-table thead th:first-child {
				position: sticky;
				left: 0;
				z-index: 11;
				box-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
			}

			.weekly-goals-table tbody td {
				border-bottom: 1px solid #f1f3f4;
				padding: 10px 8px;
				text-align: center;
				vertical-align: middle;
			}

			.weekly-goals-table tbody td:first-child {
				position: sticky;
				left: 0;
				background-color: inherit;
				z-index: 9;
				box-shadow: 2px 0 4px rgba(0, 0, 0, 0.1);
			}

			.weekly-goals-table .team-row {
				background-color: #f1f3f4;
				font-weight: bold;
				border-bottom: 2px solid #dee2e6;
			}

			.weekly-goals-table .client-row {
				background-color: #f9f9f9;
				font-weight: 600;
				border-bottom: 1px solid #e9ecef;
			}

			.weekly-goals-table .agent-row {
				background-color: #fefefe;
				border-bottom: 1px solid #f1f3f4;
			}

			.weekly-goals-table .total-row {
				background-color: #f5f5f5;
				font-weight: bold;
				border-top: 2px solid #6c757d;
				border-bottom: 2px solid #6c757d;
			}

			.weekly-goals-table .grand-total-row {
				background-color: #e9ecef;
				font-weight: bold;
				border-top: 3px solid #495057;
				border-bottom: 3px solid #495057;
			}

			.weekly-goals-table .name-cell {
				text-align: left;
				padding-left: 15px;
			}

			.weekly-goals-table .team-name {
				padding-left: 15px;
			}

			.weekly-goals-table .client-name {
				padding-left: 35px;
			}

			.weekly-goals-table .agent-name {
				padding-left: 55px;
			}

			.table-container {
				overflow-x: auto;
				max-height: 80vh;
				border: 1px solid #e9ecef;
				border-radius: 6px;
				box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
			}

			.section-title {
				color: #333;
				padding: 15px 0 10px 0;
				margin-bottom: 10px;
				font-weight: bold;
				font-size: 16px;
				border-bottom: 1px solid #e9ecef;
			}
		</style>

		<div class="section-title">
			Weekly Goals Report V2
		</div>

		<!-- Loading message -->
		<div ng-show="isLoading" style="text-align: center; padding: 40px; color: #6c757d; background-color: #f8f9fa; border: 1px solid #e9ecef; border-radius: 6px; margin-bottom: 20px;">
			<i class="fa fa-spinner fa-spin" style="font-size: 24px; margin-bottom: 10px;"></i>
			<div style="font-size: 16px; font-weight: 500;">Loading Weekly Goals Data...</div>
			<div style="font-size: 14px; margin-top: 5px;">
				Please wait while we retrieve the data for {{ selectedWeek.name }}.
			</div>
		</div>

		<!-- No data message -->
		<div ng-show="!isLoading && !hasResults" style="text-align: center; padding: 40px; color: #6c757d; background-color: #f8f9fa; border: 1px solid #e9ecef; border-radius: 6px; margin-bottom: 20px;">
			<i class="fa fa-info-circle" style="font-size: 24px; margin-bottom: 10px;"></i>
			<div style="font-size: 16px; font-weight: 500;">No Data Available</div>
			<div style="font-size: 14px; margin-top: 5px;">
				No weekly goals data found for the selected week: {{ selectedWeek.name }}.
			</div>
			<div style="font-size: 14px; margin-top: 15px;">
				Try selecting a different week or contact your administrator if you believe this is an error.
			</div>
		</div>

		<div ng-show="!isLoading && hasResults" ng-repeat="user in users" ng-if="user.clients.length">
			<div class="section-title">
				{{ user.name }}'s Team
			</div>
			<div class="table-container">
				<table class="weekly-goals-table">
					<thead>
						<tr>
							<th style="width: 350px; min-width: 350px; text-align: left;">CLIENT / AGENT</th>
							<th style="width: 80px;">Sch<br>Hrs</th>
							<th style="width: 80px;">Act<br>Hrs</th>
							<th style="width: 80px;">Dials /<br>Hr</th>
							<th style="width: 100px;">Goal</th>
							<th style="width: 100px;">Actual<br>Amt</th>
							<th style="width: 80px;">Goal<br>%</th>
							<th style="width: 80px;">Goal/<br>hr</th>
							<th style="width: 80px;">Actual/<br>hr</th>
							<th style="width: 80px;">CC & Paid Inv Gift<br>#</th>
							<th style="width: 80px;">Paid Invoice<br>#</th>
							<th style="width: 80px;">Total New Gifts<br>#</th>
							<th style="width: 80px;">Acq<br>#</th>
							<th style="width: 100px;">Mon</th>
							<th style="width: 100px;">Tues</th>
							<th style="width: 100px;">Wed</th>
							<th style="width: 100px;">Thur</th>
							<th style="width: 100px;">Fri</th>
							<th style="width: 100px;">Sat</th>
							<th style="width: 100px;">Sun</th>
							<th style="width: 80px;">CC Rate<br>#</th>
							<th style="width: 80px;">CC Rate<br>$</th>
							<th style="width: 80px;">Renew<br>Inc</th>
							<th style="width: 80px;">Add-On<br>$</th>
							<th style="width: 100px;">New<br>$</th>
							<th style="width: 100px;">Total CC<br>$</th>
							<th style="width: 100px;">Unpaid<br>Invoice $</th>
							<th style="width: 100px;">Paid<br>Invoice $</th>
						</tr>
					</thead>
					<tbody ng-repeat="client in user.clients">
						<!-- Client Header Row -->
						<tr class="client-row">
							<td class="name-cell client-name">
								<i class="fa fa-building" style="margin-right: 6px; color: #6c757d;"></i>
								<strong>{{ client.name }}</strong>
							</td>
							<td>{{ client.totals['Sch Hrs'] || 0 | number:0 }}</td>
							<td>{{ client.totals['Act Hours'] || 0 | number:0 }}</td>
							<td>{{ client.totals['Calls / Hr'] || 0 | number:0 }}</td>
							<td>{{ client.totals['Goal'] || 0 | currency:'$':0 }}</td>
							<td>{{ client.totals['Actual Amt'] || 0 | currency:'$':0 }}</td>
							<td>{{ client.totals['Goal %'] || 0 | number:0 }}%</td>
							<td>{{ client.totals['Goal/hr'] || 0 | currency:'$':0 }}</td>
							<td>{{ client.totals['Actual/hr'] || 0 | currency:'$':0 }}</td>
							<td>{{ client.totals['CC & Paid Inv Gift #'] || 0 | number:0 }}</td>
							<td>{{ client.totals['Paid Invoice #'] || 0 | number:0 }}</td>
							<td>{{ client.totals['Total New Gifts #'] || 0 | number:0 }}</td>
							<td>{{ client.totals['Acq #'] || 0 | number:0 }}</td>
							<td>{{ client.totals['Monday Total'] || 0 | currency:'$':0 }}</td>
							<td>{{ client.totals['Tuesday Total'] || 0 | currency:'$':0 }}</td>
							<td>{{ client.totals['Wednesday Total'] || 0 | currency:'$':0 }}</td>
							<td>{{ client.totals['Thursday Total'] || 0 | currency:'$':0 }}</td>
							<td>{{ client.totals['Friday Total'] || 0 | currency:'$':0 }}</td>
							<td>{{ client.totals['Saturday Total'] || 0 | currency:'$':0 }}</td>
							<td>{{ client.totals['Sunday Total'] || 0 | currency:'$':0 }}</td>
							<td>{{ client.totals['CC Rate #'] || 0 | number:0 }}%</td>
							<td>{{ client.totals['CC Rate %'] || 0 | number:0 }}%</td>
							<td>{{ client.totals['Renew Inc'] || 0 | number:0 }}%</td>
							<td>{{ client.totals['Add-On $'] || 0 | currency:'$':0 }}</td>
							<td>{{ client.totals['New $'] || 0 | currency:'$':0 }}</td>
							<td>{{ client.totals['Total CC $'] || 0 | currency:'$':0 }}</td>
							<td>{{ client.totals['Unpaid Invoice $'] || 0 | currency:'$':0 }}</td>
							<td>{{ client.totals['Paid Invoice $'] || 0 | currency:'$':0 }}</td>
						</tr>

						<!-- Agents within Client -->
						<tr ng-repeat="agent in client.agents" class="agent-row">
							<td class="name-cell agent-name">
								<i class="fa fa-user" style="margin-right: 4px; color: #6c757d;"></i>
								{{ agent['Agent Name'] }}
							</td>
							<td>{{ agent['Sch Hrs'] || 0 | number:0 }}</td>
							<td>{{ agent['Act Hours'] || 0 | number:0 }}</td>
							<td>{{ agent['Calls / Hr'] || 0 | number:0 }}</td>
							<td>{{ agent['Goal'] || 0 | currency:'$':0 }}</td>
							<td>{{ agent['Actual Amt'] || 0 | currency:'$':0 }}</td>
							<td>{{ agent['Goal %'] || 0 | number:0 }}%</td>
							<td>{{ agent['Goal/hr'] || 0 | currency:'$':0 }}</td>
							<td>{{ agent['Actual/hr'] || 0 | currency:'$':0 }}</td>
							<td>{{ agent['CC & Paid Inv Gift #'] || 0 | number:0 }}</td>
							<td>{{ agent['Paid Invoice #'] || 0 | number:0 }}</td>
							<td>{{ agent['Total New Gifts #'] || 0 | number:0 }}</td>
							<td>{{ agent['Acq #'] || 0 | number:0 }}</td>
							<td>{{ agent['Monday Total'] || 0 | currency:'$':0 }}</td>
							<td>{{ agent['Tuesday Total'] || 0 | currency:'$':0 }}</td>
							<td>{{ agent['Wednesday Total'] || 0 | currency:'$':0 }}</td>
							<td>{{ agent['Thursday Total'] || 0 | currency:'$':0 }}</td>
							<td>{{ agent['Friday Total'] || 0 | currency:'$':0 }}</td>
							<td>{{ agent['Saturday Total'] || 0 | currency:'$':0 }}</td>
							<td>{{ agent['Sunday Total'] || 0 | currency:'$':0 }}</td>
							<td>{{ agent['CC Rate #'] || 0 | number:0 }}%</td>
							<td>{{ agent['CC Rate $'] || 0 | number:0 }}%</td>
							<td>{{ agent['Renew Inc'] || 0 | number:0 }}%</td>
							<td>{{ agent['Add-On $'] || 0 | currency:'$':0 }}</td>
							<td>{{ agent['New $'] || 0 | currency:'$':0 }}</td>
							<td>{{ agent['Total CC $'] || 0 | currency:'$':0 }}</td>
							<td>{{ agent['Unpaid Invoice $'] || 0 | currency:'$':0 }}</td>
							<td>{{ agent['Paid Invoice $'] || 0 | currency:'$':0 }}</td>
						</tr>
					</tbody>

					<!-- Team Totals Row -->
					<tr class="team-row">
						<td class="name-cell">
							<i class="fa fa-users" style="margin-right: 8px; color: #6c757d;"></i>
							<strong>Team Lead Totals</strong>
						</td>
						<td><strong>{{ user.totals['Sch Hrs'] || 0 | number:0 }}</strong></td>
						<td><strong>{{ user.totals['Act Hours'] || 0 | number:0 }}</strong></td>
						<td><strong>{{ user.totals['Calls / Hr'] || 0 | number:0 }}</strong></td>
						<td><strong>{{ user.totals['Goal'] || 0 | currency:'$':0 }}</strong></td>
						<td><strong>{{ user.totals['Actual Amt'] || 0 | currency:'$':0 }}</strong></td>
						<td><strong>{{ user.totals['Goal %'] || 0 | number:0 }}%</strong></td>
						<td><strong>{{ user.totals['Goal/hr'] || 0 | currency:'$':0 }}</strong></td>
						<td><strong>{{ user.totals['Actual/hr'] || 0 | currency:'$':0 }}</strong></td>
						<td><strong>{{ user.totals['CC & Paid Inv Gift #'] || 0 | number:0 }}</strong></td>
						<td><strong>{{ user.totals['Paid Invoice #'] || 0 | number:0 }}</strong></td>
						<td><strong>{{ user.totals['Total New Gifts #'] || 0 | number:0 }}</strong></td>
						<td><strong>{{ user.totals['Acq #'] || 0 | number:0 }}</strong></td>
						<td><strong>{{ user.totals['Monday Total'] || 0 | currency:'$':0 }}</strong></td>
						<td><strong>{{ user.totals['Tuesday Total'] || 0 | currency:'$':0 }}</strong></td>
						<td><strong>{{ user.totals['Wednesday Total'] || 0 | currency:'$':0 }}</strong></td>
						<td><strong>{{ user.totals['Thursday Total'] || 0 | currency:'$':0 }}</strong></td>
						<td><strong>{{ user.totals['Friday Total'] || 0 | currency:'$':0 }}</strong></td>
						<td><strong>{{ user.totals['Saturday Total'] || 0 | currency:'$':0 }}</strong></td>
						<td><strong>{{ user.totals['Sunday Total'] || 0 | currency:'$':0 }}</strong></td>
						<td><strong>{{ user.totals['CC Rate #'] || 0 | number:0 }}%</strong></td>
						<td><strong>{{ user.totals['CC Rate $'] || 0 | number:0 }}%</strong></td>
						<td><strong>{{ user.totals['Renew Inc'] || 0 | number:0 }}%</strong></td>
						<td><strong>{{ user.totals['Add-On $'] || 0 | currency:'$':0 }}</strong></td>
						<td><strong>{{ user.totals['New $'] || 0 | currency:'$':0 }}</strong></td>
						<td><strong>{{ user.totals['Total CC $'] || 0 | currency:'$':0 }}</strong></td>
						<td><strong>{{ user.totals['Unpaid Invoice $'] || 0 | currency:'$':0 }}</strong></td>
						<td><strong>{{ user.totals['Paid Invoice $'] || 0 | currency:'$':0 }}</strong></td>
					</tr>
				</table>
			</div>
		</div>

		<!-- Grand Total Section -->
		<div ng-show="!isLoading && hasResults && grandTotals && users && users.length > 0" style="margin-top: 30px;">
			<div class="section-title">
				Grand Total
			</div>
			<div class="table-container">
				<table class="weekly-goals-table">
					<thead>
						<tr>
							<th style="width: 350px; min-width: 350px; text-align: left;">SUMMARY</th>
							<th style="width: 80px;">Sch<br>Hrs</th>
							<th style="width: 80px;">Act<br>Hrs</th>
							<th style="width: 80px;">Dials /<br>Hr</th>
							<th style="width: 100px;">Goal</th>
							<th style="width: 100px;">Actual<br>Amt</th>
							<th style="width: 80px;">Goal<br>%</th>
							<th style="width: 80px;">Goal/<br>hr</th>
							<th style="width: 80px;">Actual/<br>hr</th>
							<th style="width: 80px;">CC & Paid Inv Gift<br>#</th>
							<th style="width: 80px;">Paid Invoice<br>#</th>
							<th style="width: 80px;">Total New Gifts<br>#</th>
							<th style="width: 80px;">Acq<br>#</th>
							<th style="width: 100px;">Mon</th>
							<th style="width: 100px;">Tues</th>
							<th style="width: 100px;">Wed</th>
							<th style="width: 100px;">Thur</th>
							<th style="width: 100px;">Fri</th>
							<th style="width: 100px;">Sat</th>
							<th style="width: 100px;">Sun</th>
							<th style="width: 80px;">CC Rate<br>#</th>
							<th style="width: 80px;">CC Rate<br>$</th>
							<th style="width: 80px;">Renew<br>Inc</th>
							<th style="width: 80px;">Add-On<br>$</th>
							<th style="width: 100px;">New<br>$</th>
							<th style="width: 100px;">Total CC<br>$</th>
							<th style="width: 100px;">Unpaid<br>Invoice $</th>
							<th style="width: 100px;">Paid<br>Invoice $</th>
						</tr>
					</thead>
					<tbody>
						<tr class="grand-total-row">
							<td class="name-cell">
								<i class="fa fa-calculator" style="margin-right: 8px; color: #6c757d;"></i>
								<strong>GRAND TOTAL</strong>
							</td>
							<td><strong>{{ grandTotals['Sch Hrs'] || 0 | number:0 }}</strong></td>
							<td><strong>{{ grandTotals['Act Hours'] || 0 | number:0 }}</strong></td>
							<td><strong>{{ grandTotals['Calls / Hr'] || 0 | number:0 }}</strong></td>
							<td><strong>{{ grandTotals['Goal'] || 0 | currency:'$':0 }}</strong></td>
							<td><strong>{{ grandTotals['Actual Amt'] || 0 | currency:'$':0 }}</strong></td>
							<td><strong>{{ grandTotals['Goal %'] || 0 | number:0 }}%</strong></td>
							<td><strong>{{ grandTotals['Goal/hr'] || 0 | currency:'$':0 }}</strong></td>
							<td><strong>{{ grandTotals['Actual/hr'] || 0 | currency:'$':0 }}</strong></td>
							<td><strong>{{ grandTotals['CC & Paid Inv Gift #'] || 0 | number:0 }}</strong></td>
							<td><strong>{{ grandTotals['Paid Invoice #'] || 0 | number:0 }}</strong></td>
							<td><strong>{{ grandTotals['Total New Gifts #'] || 0 | number:0 }}</strong></td>
							<td><strong>{{ grandTotals['Acq #'] || 0 | number:0 }}</strong></td>
							<td><strong>{{ grandTotals['Monday Total'] || 0 | currency:'$':0 }}</strong></td>
							<td><strong>{{ grandTotals['Tuesday Total'] || 0 | currency:'$':0 }}</strong></td>
							<td><strong>{{ grandTotals['Wednesday Total'] || 0 | currency:'$':0 }}</strong></td>
							<td><strong>{{ grandTotals['Thursday Total'] || 0 | currency:'$':0 }}</strong></td>
							<td><strong>{{ grandTotals['Friday Total'] || 0 | currency:'$':0 }}</strong></td>
							<td><strong>{{ grandTotals['Saturday Total'] || 0 | currency:'$':0 }}</strong></td>
							<td><strong>{{ grandTotals['Sunday Total'] || 0 | currency:'$':0 }}</strong></td>
							<td><strong>{{ grandTotals['CC Rate #'] || 0 | number:0 }}%</strong></td>
							<td><strong>{{ grandTotals['CC Rate $'] || 0 | number:0 }}%</strong></td>
							<td><strong>{{ grandTotals['Renew Inc'] || 0 | number:0 }}%</strong></td>
							<td><strong>{{ grandTotals['Add-On $'] || 0 | currency:'$':0 }}</strong></td>
							<td><strong>{{ grandTotals['New $'] || 0 | currency:'$':0 }}</strong></td>
							<td><strong>{{ grandTotals['Total CC $'] || 0 | currency:'$':0 }}</strong></td>
							<td><strong>{{ grandTotals['Unpaid Invoice $'] || 0 | currency:'$':0 }}</strong></td>
							<td><strong>{{ grandTotals['Paid Invoice $'] || 0 | currency:'$':0 }}</strong></td>
						</tr>
					</tbody>
				</table>
			</div>
		</div>
	</div>
</div>