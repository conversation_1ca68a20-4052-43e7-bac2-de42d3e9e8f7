'use strict';

angular.module('dialerFrontendApp')
	.controller('ReportWeeklyGoalsV2Ctrl', function (APP_SETTINGS, $window, $scope, $rootScope, moment, _, Sweet<PERSON><PERSON>t, Agent, Campaign, Client, User) {
		$scope.users = [];
		$scope.availableWeeks = [];
		$scope.hasResults = false;
		$scope.isLoading = false;

		$rootScope.pageTitle = 'Report | Weekly Goals V2';

		$scope.isWeeklyGoalsV2Allowed = function() {
			// Array of allowed user IDs - add specific user IDs here
			var allowedUserIds = [
				0,10,12,297,406,660
			];

			// Check if current user ID is in the allowed list
			return allowedUserIds.includes($rootScope.loggedInUser.id);
		};

		// Check access before proceeding
		if (!$scope.isWeeklyGoalsV2Allowed()) {
			SweetAlert.swal('Access Denied', 'You do not have permission to access Weekly Goals V2.', 'error');
			$window.history.back();
			return;
		}

		var now = moment().day('Monday');
		var blankTotals = {
			'Sch Hrs': 0,
			'Act Hours': 0,
			'Calls / Hr': 0,
			'Goal': 0,
			'Actual Amt': 0,
			'Goal/hr': 0,
			'Actual/hr': 0,
			'Acq #': 0,
			'Monday Total': 0,
			'Tuesday Total': 0,
			'Wednesday Total': 0,
			'Thursday Total': 0,
			'Friday Total': 0,
			'Saturday Total': 0,
			'Sunday Total': 0,
			'CC Rate #': 0,
			'CC Rate $': 0,
			'Renew Inc': 0,
			'Add-On $': 0,
			'CC & Paid Inv Gift #': 0,
			'Paid Invoice #': 0,
			'Total New Gifts #': 0,
			'New $': 0,
			'Total CC $': 0,
			'Unpaid Invoice $': 0,
			'Paid Invoice $': 0
		};

		for (var i = 0; i < 52; i++) {
			var start = moment().day('Monday').subtract(i, 'weeks');
			var end = moment(start).add(6, 'days').endOf('day').utc().format('YYYY-MM-DD HH:mm:ss')
			$scope.availableWeeks.push({
				name: start.format('DD-MMM-YYYY'),
				start: start.startOf('day').utc().format('YYYY-MM-DD HH:mm:ss'),
				end: end
			})
		}

		$scope.selectedWeek = _.first($scope.availableWeeks);

		$scope.getResults = function () {
			$scope.users = [];
			$scope.hasResults = false;
			$scope.isLoading = true;

			Agent.getWeeklyGoalsV2View({
				startDate: $scope.selectedWeek.start,
				endDate: $scope.selectedWeek.end
			}).$promise.then(function (results) {
				$scope.isLoading = false;

				// Check if we have any data
				if (!results || !results.rows || results.rows.length === 0) {
					$scope.hasResults = false;
					$scope.users = [];
					$scope.grandTotals = null;
					return;
				}

				$scope.hasResults = true;
				processResults(results);
			}).catch(function (err) {
				$scope.isLoading = false;
				$scope.hasResults = false;
				console.log(err.message ? err.message : err);
				SweetAlert.swal('Error', 'Failed to load weekly goals data. Please try again.', 'error');
			})
		};


		function processResults(results) {
			results.rows.forEach(function (result) {
				addResultToUser(result);
			});

			processTotals(results.totals);
		}

		function addResultToUser(result) {
			var user = _.findWhere($scope.users, {
				name: result.Supervisor
			});

			if (!user) {
				user = {
					name: result.Supervisor,
					clients: []
				};
				$scope.users.push(user);
			}

			addResultToClient(user, result);
		}

		function addResultToClient(user, result) {
			var client = _.findWhere(user.clients, {
				name: result['Campaign Name']
			});

			if (client) {
				client.agents.push(result);
			} else {
				client = {
					name: result['Campaign Name'],
					agents: [result]
				};
				user.clients.push(client);
			}
		}

		function defaultToZero(value) {
			return typeof value === 'number' ? value : 0;
		}

		function processTotals(totals) {
			$scope.grandTotals = angular.copy(blankTotals);
			$scope.users.forEach(function (user) {
				try {
					user.totals = angular.copy(blankTotals);
					user.clients.forEach(function (client) {
						try {
							client.totals = angular.copy(blankTotals);
							client.agents.forEach(function (agent) {
								for (var prop in client.totals) {
									client.totals[prop] += agent[prop];
								}
							})

							if (totals[client.name]) {
								client.totals['Actual/hr'] = defaultToZero(client.totals['Actual Amt'] / client.totals['Act Hours']);
								client.totals['Acq #'] = totals[client.name]['Acq #'];
								client.totals['CC Rate #'] = totals[client.name]['CC Rate #'];
								client.totals['CC Rate $'] = totals[client.name]['CC Rate $'];
								client.totals['Renew Inc'] = totals[client.name]['Renew Inc'];
								client.totals['Add-On $'] = totals[client.name]['Add-On $'];
								client.totals['Total CC $'] = totals[client.name]['Total CC $'];
								client.totals['Unpaid Invoice $'] = totals[client.name]['Unpaid Invoice $'];
								client.totals['Paid Invoice $'] = totals[client.name]['Paid Invoice $'];
								client.totals['Goal %'] = (client.totals['Actual Amt'] / client.totals['Goal']) * 100;
								client.totals['Goal/hr'] = Math.round(client.totals['Goal'] / (client.totals['Sch Hrs'] || 1));
							}

							for (var prop in user.totals) {
								user.totals[prop] += client.totals[prop];
							}
						} catch (err) {
							console.log(client)
							console.log(err)
						}
					})

					if (totals[user.name]) {
						user.totals['Actual/hr'] = defaultToZero(user.totals['Actual Amt'] / user.totals['Act Hours']);
						user.totals['Acq #'] = totals[user.name]['Acq #'];
						user.totals['CC Rate #'] = totals[user.name]['CC Rate #'];
						user.totals['CC Rate $'] = totals[user.name]['CC Rate $'];
						user.totals['Renew Inc'] = totals[user.name]['Renew Inc'];
						user.totals['Add-On $'] = totals[user.name]['Add-On $'];
						user.totals['Total CC $'] = totals[user.name]['Total CC $'];
						user.totals['Unpaid Invoice $'] = totals[user.name]['Unpaid Invoice $'];
						user.totals['Paid Invoice $'] = totals[user.name]['Paid Invoice $'];
						user.totals['Goal %'] = (user.totals['Actual Amt'] / user.totals['Goal']) * 100;
						user.totals['Goal/hr'] = Math.round(user.totals['Goal'] / (user.totals['Sch Hrs'] || 1));
					}

					for (var prop in user.totals) {
						$scope.grandTotals[prop] += user.totals[prop];
					}
				} catch (err) {
					console.log(user)
					console.log(err)
				}
			})

			$scope.grandTotals['Actual/hr'] = defaultToZero($scope.grandTotals['Actual Amt'] / $scope.grandTotals['Act Hours']);
			$scope.grandTotals['Acq #'] = totals.grandTotal['Acq #'];
			$scope.grandTotals['CC Rate #'] = totals.grandTotal['CC Rate #'];
			$scope.grandTotals['CC Rate $'] = totals.grandTotal['CC Rate $'];
			$scope.grandTotals['Renew Inc'] = totals.grandTotal['Renew Inc'];
			$scope.grandTotals['Add-On $'] = totals.grandTotal['Add-On $'];
			$scope.grandTotals['Total CC $'] = totals.grandTotal['Total CC $'];
			$scope.grandTotals['Unpaid Invoice $'] = totals.grandTotal['Unpaid Invoice $'];
			$scope.grandTotals['Paid Invoice $'] = totals.grandTotal['Paid Invoice $'];
			$scope.grandTotals['Goal %'] = ($scope.grandTotals['Actual Amt'] / $scope.grandTotals['Goal']) * 100;
			$scope.grandTotals['Goal/hr'] = Math.round($scope.grandTotals['Goal'] / ($scope.grandTotals['Sch Hrs'] || 1));
			$scope.grandTotals.user = _.last($scope.users).name;
		}

		$scope.getResults();
	})