'use strict';

angular.module('dialerFrontendApp')
    .factory('CallResultField', function ($resource, APP_SETTINGS) {
        return $resource(APP_SETTINGS.BASE_API_URL + 'callresultfields/:id', {
            id: '@id'
        }, {
            update: {
                method: 'PUT'
            }
        });
    })
    .factory('CallResultFieldGroup', function ($resource, APP_SETTINGS) {
        return $resource(APP_SETTINGS.BASE_API_URL + 'callresultfieldgroups/:id', {
            id: '@id'
        }, {
            update: {
                method: 'PUT'
            }
        });
    })
    .factory('CallResultFieldType', function ($resource, APP_SETTINGS) {
        return $resource(APP_SETTINGS.BASE_API_URL + 'callresultfieldtypes/:id', {
            id: '@id'
        }, {
            update: {
                method: 'PUT'
            }
        });
    })
    .factory('CallResultFieldOption', function ($resource, APP_SETTINGS) {
        return $resource(APP_SETTINGS.BASE_API_URL + 'callresultfieldoptions/:id', {
            id: '@id'
        }, {
            update: {
                method: 'PUT'
            }
        });
    })
    .factory('CallResultFieldValue', function ($resource, APP_SETTINGS) {
        return $resource(APP_SETTINGS.BASE_API_URL + 'callresultfieldvalues/:id', {
            id: '@id'
        }, {
            update: {
                method: 'PUT'
            }
        });
    })
    .factory('CallRecord', function ($resource, APP_SETTINGS) {
        return $resource(APP_SETTINGS.BASE_API_URL + 'callrecords/:id', {
            id: '@id'
        }, {
            update: {
                method: 'PUT'
            },
            query: {
                method: 'GET',
                url: APP_SETTINGS.BASE_API_URL + 'callrecords/page/:page'
            },
        });
    })
    .factory('Client', function ($resource, APP_SETTINGS) {
        return $resource(APP_SETTINGS.BASE_API_URL + 'clients/:id', {
            id: '@id'
        }, {
            update: {
                method: 'PUT'
            },
            getCostings: {
                method: 'GET',
                isArray: true,
                url: APP_SETTINGS.BASE_API_URL + 'clients/:id/costings'
            },
            getCampaigns: {
                method: 'GET',
                isArray: true,
                url: APP_SETTINGS.BASE_API_URL + 'clients/:id/campaigns'
            },
            getNotes: {
                method: 'GET',
                isArray: true,
                url: APP_SETTINGS.BASE_API_URL + 'clients/:id/campaignnotes'
            },
            getDemoInvoice: {
                method: 'GET',
                url: APP_SETTINGS.BASE_API_URL + 'clients/:id/demoinvoice'
            }
        });
    })
    .factory('CampaignProduct', function ($resource, APP_SETTINGS) {
        return $resource(APP_SETTINGS.BASE_API_URL + 'campaignproducts/:id', {
            id: '@id'
        }, {
            update: {
                method: 'PUT'
            },
            query: {
                method: 'GET',
                isArray: true
            }
        });
    })
    .factory('CampaignNotes', function ($resource, APP_SETTINGS) {
        return $resource(APP_SETTINGS.BASE_API_URL + 'campaignnotes/:id', {
            id: '@id'
        }, {
            update: {
                method: 'PUT'
            },
            query: {
                method: 'GET',
                isArray: true
            }
        });
    })
    .factory('ClientCosting', function ($resource, APP_SETTINGS) {
        return $resource(APP_SETTINGS.BASE_API_URL + 'clientcostings/:id', {
            id: '@id'
        }, {
            update: {
                method: 'PUT'
            },
            query: {
                method: 'GET',
                isArray: true
            }
        });
    })
    .factory('ClientCampaigns', function ($resource, APP_SETTINGS) {
        return $resource(APP_SETTINGS.BASE_API_URL + 'clients/:id/campaigns', {
            id: '@id'
        }, {
            update: {
                method: 'PUT',
            },
            query: {
                method: 'GET',
                isArray: true
            },
            count: {
                method: 'GET',
                params: {
                    countOnly: true
                }
            }
        });
    })
    .factory('ClientLeads', function ($resource, APP_SETTINGS) {
        return $resource(APP_SETTINGS.BASE_API_URL + 'clients/:id/leads', {
            id: '@id'
        }, {
            update: {
                method: 'PUT',
            },
            query: {
                method: 'GET',
                isArray: true
            },
            count: {
                method: 'GET',
                params: {
                    countOnly: true
                }
            }
        });
    })
    .factory('CampaignStageAgent', function ($resource, APP_SETTINGS) {
        return $resource(APP_SETTINGS.BASE_API_URL + 'campaignstages/:id/agents', {
            id: '@id'
        }, {
            update: {
                method: 'PUT',
            },
            query: {
                method: 'GET',
                isArray: true
            },
            count: {
                method: 'GET',
                params: {
                    countOnly: true
                }
            }
        });
    })
    .factory('Campaign', function ($resource, APP_SETTINGS) {
        return $resource(APP_SETTINGS.BASE_API_URL + 'campaigns/:id', {
            id: '@id'
        }, {
            update: {
                method: 'PUT'
            },
            getLeadTypes: {
                method: 'GET',
                isArray: true,
                url: APP_SETTINGS.BASE_API_URL + 'campaigns/:id/leads/types'
            },
            getLeadSubTypes: {
                method: 'GET',
                isArray: true,
                url: APP_SETTINGS.BASE_API_URL + 'campaigns/:id/leads/subtypes'
            },
            getProjections: {
                method: 'GET',
                isArray: true,
                url: APP_SETTINGS.BASE_API_URL + 'campaigns/:id/projections'
            },
            getInvoices: {
                method: 'GET',
                isArray: true,
                url: APP_SETTINGS.BASE_API_URL + 'campaigns/:id/invoices'
            },
            getCampaignStages: {
                method: 'GET',
                isArray: true,
                url: APP_SETTINGS.BASE_API_URL + 'campaigns/:id/stages'
            },
            updateAgentTarget: {
                method: 'PUT',
                url: APP_SETTINGS.BASE_API_URL + 'campaigns/agenttargets'
            },
            createAgentTarget: {
                method: 'POST',
                url: APP_SETTINGS.BASE_API_URL + 'campaigns/agenttargets'
            },
            getAgentTargets: {
                method: 'GET',
                isArray: true,
                url: APP_SETTINGS.BASE_API_URL + 'campaigns/:id/agenttargets'
            },
            getAgents: {
                method: 'GET',
                isArray: true,
                url: APP_SETTINGS.BASE_API_URL + 'campaigns/:id/agents'
            },
            getCampaignLeadAudits: {
                method: 'GET',
                isArray: true,
                url: APP_SETTINGS.BASE_API_URL + 'campaigns/:id/leads/audits',
                params: {
                    page: ':page'
                }
            },
            getCallHistory: {
                method: 'GET',
                isArray: true,
                url: APP_SETTINGS.BASE_API_URL + 'campaigns/:id/callhistory'
            },
            getPledges: {
                method: 'GET',
                isArray: true,
                url: APP_SETTINGS.BASE_API_URL + 'campaigns/:id/callhistory/pledges'
            },
            getSales: {
                method: 'GET',
                isArray: true,
                url: APP_SETTINGS.BASE_API_URL + 'campaigns/:id/callhistory/sales'
            },
            getRefusals: {
                method: 'GET',
                isArray: true,
                url: APP_SETTINGS.BASE_API_URL + 'campaigns/:id/callhistory/refusals'
            },
            getLeadInvoices: {
                method: 'GET',
                isArray: true,
                url: APP_SETTINGS.BASE_API_URL + 'campaigns/:id/leads/:leadid/invoices'
            },
            moveLeadToCampaignStage: {
                method: 'POST',
                url: APP_SETTINGS.BASE_API_URL + 'campaigns/:id/transitionlead'
            },
            getStats: {
                method: 'GET',
                url: APP_SETTINGS.BASE_API_URL + 'campaigns/:id/stats'
            },
            getProgress: {
                method: 'GET',
                url: APP_SETTINGS.BASE_API_URL + 'campaigns/:id/progress'
            },
            getProducts: {
                method: 'GET',
                isArray: true,
                url: APP_SETTINGS.BASE_API_URL + 'campaigns/:id/products'
            },
            getProductsWithPaging: {
                method: 'GET',
                url: APP_SETTINGS.BASE_API_URL + 'campaigns/:id/products/page/:page',
                params: {
                    orderby: ':orderby',
                    dir: ':dir'
                }
            },
            getUploads: {
                method: 'GET',
                isArray: true,
                url: APP_SETTINGS.BASE_API_URL + 'campaigns/:id/uploads'
            },
            getNotes: {
                method: 'GET',
                isArray: true,
                url: APP_SETTINGS.BASE_API_URL + 'campaigns/:id/campaignnotes'
            },
            getCallRecords: {
                method: 'GET',
                url: APP_SETTINGS.BASE_API_URL + 'campaigns/:id/callrecords',
                isArray: true
            },
            getLeadTypeAndGroup: {
                method: 'GET',
                url: APP_SETTINGS.BASE_API_URL + 'campaigns/:id/skillsandsubskills',
                isArray: true
            },
            getStageDispositions: {
                method: 'GET',
                url: APP_SETTINGS.BASE_API_URL + 'campaigns/:id/campaignstage/:stageid/dispositions'
            },
            getCallbacks: {
                method: 'GET',
                isArray: true,
                url: APP_SETTINGS.BASE_API_URL + 'campaigns/:id/callbacks'
            },
            deleteProducts: {
                method: 'DELETE',
                url: APP_SETTINGS.BASE_API_URL + 'campaignproducts/campaigns/:id'
            },
            getDocs: {
                method: 'GET',
                isArray: true,
                url: APP_SETTINGS.BASE_API_URL + 'campaigns/:id/trainingdocs'
            },
            getChanges: {
                method: 'GET',
                isArray: true,
                url: APP_SETTINGS.BASE_API_URL + 'campaigns/:id/audit'
            }
        });
    })
    .factory('CampaignStage', function ($resource, APP_SETTINGS) {
        return $resource(APP_SETTINGS.BASE_API_URL + 'campaignstages/:id', {
            id: '@id'
        }, {
            update: {
                method: 'PUT'
            },
            getAgents: {
                method: 'GET',
                isArray: true,
                url: APP_SETTINGS.BASE_API_URL + 'campaignstages/:id/agents'
            },
            setAgents: {
                method: 'PUT',
                isArray: true,
                url: APP_SETTINGS.BASE_API_URL + 'campaignstages/:id/agents'
            },
            getLeadTypes: {
                method: 'GET',
                isArray: true,
                url: APP_SETTINGS.BASE_API_URL + 'campaignstages/:id/leads/types'
            },
            getLeadSubTypes: {
                method: 'GET',
                isArray: true,
                url: APP_SETTINGS.BASE_API_URL + 'campaignstages/:id/leads/subtypes'
            },
            getDispositions: {
                method: 'GET',
                isArray: true,
                url: APP_SETTINGS.BASE_API_URL + 'campaignstages/:id/dispositions'
            },
            getWorkflows: {
                method: 'GET',
                isArray: true,
                url: APP_SETTINGS.BASE_API_URL + 'campaignstages/:id/workflows'
            },
            setWorkflows: {
                method: 'PUT',
                url: APP_SETTINGS.BASE_API_URL + 'campaignstages/:id/workflows'
            },
            setAgentTargets: {
                method: 'PUT',
                url: APP_SETTINGS.BASE_API_URL + 'campaignstages/:id/agenttargets'
            },
            getDateTimeRules: {
                method: 'GET',
                isArray: true,
                url: APP_SETTINGS.BASE_API_URL + 'campaignstages/:id/datetimerules'
            },
            getLeadCount: {
                method: 'GET',
                url: APP_SETTINGS.BASE_API_URL + 'campaignstages/:id/leadcount'
            },
            addCallAttempts: {
                method: 'POST',
                url: APP_SETTINGS.BASE_API_URL + 'campaignstages/:id/callattempts'
            },
            removeCallAttempts: {
                method: 'PUT',
                url: APP_SETTINGS.BASE_API_URL + 'campaignstages/:id/callattempts'
            },
            exhaustLead: {
                method: 'POST',
                params: {
                    id: '@id',
                    leadId: '@leadId'
                },
                url: APP_SETTINGS.BASE_API_URL + 'campaignstages/:id/lead/:leadId/exhaust'
            },
            getLeads: {
                method: 'GET',
                url: APP_SETTINGS.BASE_API_URL + 'campaignstages/:id/leads',
                params: {
                    orderby: ':orderby',
                    dir: ':dir',
                    page: ':page',
                    filters: ':filters'
                }
            },
            resetLeads: {
                method: 'PUT',
                url: APP_SETTINGS.BASE_API_URL + 'campaignstages/:id/resetleads'
            },
            deleteLeads: {
                method: 'DELETE',
                url: APP_SETTINGS.BASE_API_URL + 'campaignstages/:id/leads'
            },
        });
    })
    .factory('CampaignStageDateTimeRule', function ($resource, APP_SETTINGS) {
        return $resource(APP_SETTINGS.BASE_API_URL + 'campaignstagedatetimerules/:id', {
            id: '@id'
        }, {
            update: {
                method: 'PUT'
            }
        });
    })
    .factory('CampaignTrainingDoc', function ($resource, APP_SETTINGS) {
        return $resource(APP_SETTINGS.BASE_API_URL + 'campaigntrainingdocs/:id', {
            id: '@id'
        }, {
            update: {
                method: 'PUT'
            }
        });
    })
    .factory('CampaignProjections', function ($resource, APP_SETTINGS) {
        return $resource(APP_SETTINGS.BASE_API_URL + 'campaignprojections/:id', {
            id: '@id'
        }, {
            update: {
                method: 'PUT'
            }
        });
    })
    .factory('Sale', function ($resource, APP_SETTINGS) {
        return $resource(APP_SETTINGS.BASE_API_URL + 'sales/:id', {
            id: '@id'
        }, {
            update: {
                method: 'PUT'
            },
            query: {
                method: 'GET',
                isArray: true
            }
        });
    })
    .factory('Invoice', function ($resource, APP_SETTINGS) {
        return $resource(APP_SETTINGS.BASE_API_URL + 'invoices/:id', {
            id: '@id'
        }, {
            update: {
                method: 'PUT'
            },
            query: {
                method: 'GET',
                url: APP_SETTINGS.BASE_API_URL + 'invoices/page/:page',
                params: {
                    orderby: ':orderby',
                    dir: ':dir',
                    filters: ':filters'
                }
            },
            getHistory: {
                method: 'GET',
                isArray: true,
                url: APP_SETTINGS.BASE_API_URL + 'invoices/:id/history'
            },
            getPayments: {
                method: 'GET',
                isArray: true,
                url: APP_SETTINGS.BASE_API_URL + 'invoices/:id/payments'
            },
            getEvents: {
                method: 'GET',
                isArray: true,
                url: APP_SETTINGS.BASE_API_URL + 'invoices/:id/events'
            },
            getByCallResultId: {
                method: 'GET',
                url: APP_SETTINGS.BASE_API_URL + 'invoices/getbycallresultid/:callresultId'
            },
            generate: {
                method: 'POST',
                url: APP_SETTINGS.BASE_API_URL + 'invoices/:id/generate'
            },
            generateTest: {
                method: 'POST',
                url: APP_SETTINGS.BASE_API_URL + 'invoices/:id/generatetest'
            },
            writeOff: {
                method: 'POST',
                url: APP_SETTINGS.BASE_API_URL + 'invoices/:id/writeoff'
            }
        });
    })
    .factory('BroadcastMessage', function ($resource, APP_SETTINGS) {
        return $resource(APP_SETTINGS.BASE_API_URL + 'broadcastmessages/:id', {
            id: '@id'
        }, {
            update: {
                method: 'PUT'
            },
            query: {
                method: 'GET',
                isArray: true
            }
        });
    })
    .factory('CampaignLeads', function ($resource, APP_SETTINGS) {
        return $resource(APP_SETTINGS.BASE_API_URL + 'campaigns/:id/leads', {
            id: '@id'
        }, {
            update: {
                method: 'PUT',
            },
            query: {
                method: 'GET',
                url: APP_SETTINGS.BASE_API_URL + 'campaigns/:id/leads/page/:page',
                params: {
                    page: ':page',
                    orderby: ':orderby',
                    dir: ':dir',
                    filters: ':filters',
                    prefilters: ':prefilters'
                }
            },
            count: {
                method: 'GET',
                params: {
                    countOnly: true
                }
            },
            delete: {
                method: 'DELETE',
                url: APP_SETTINGS.BASE_API_URL + 'campaigns/:id/leads/delete',
                params: {
                    leadId: ':leadId'
                }
            }
        });
    })
    .factory('CampaignCallAttempts', function ($resource, APP_SETTINGS) {
        return $resource(APP_SETTINGS.BASE_API_URL + 'campaigns/:id/callattempts', {
            id: '@id'
        }, {
            query: {
                method: 'GET',
                isArray: true
            },
            count: {
                method: 'GET',
                params: {
                    countOnly: true
                }
            }
        });
    })
    .factory('LeadCallAttempts', function ($resource, APP_SETTINGS) {
        return $resource(APP_SETTINGS.BASE_API_URL + 'leads/:id/callattempts', {
            id: '@id'
        }, {
            query: {
                method: 'GET',
                isArray: true
            },
            count: {
                method: 'GET',
                params: {
                    countOnly: true
                }
            }
        });
    })
    .factory('Agent', function ($resource, APP_SETTINGS) {
        return $resource(APP_SETTINGS.BASE_API_URL + 'agents/:id', {
            id: '@id'
        }, {
            update: {
                method: 'PUT'
            },
            getCallCountToday: {
                method: 'GET',
                url: APP_SETTINGS.BASE_API_URL + 'agents/:id/callcounttoday'
            },
            getAvgDurationToday: {
                method: 'GET',
                url: APP_SETTINGS.BASE_API_URL + 'agents/:id/avgdurationtoday'
            },
            getAvgWrapUpDurationToday: {
                method: 'GET',
                url: APP_SETTINGS.BASE_API_URL + 'agents/:id/avgwrapupdurationtoday'
            },
            getAnsweredPercentToday: {
                method: 'GET',
                url: APP_SETTINGS.BASE_API_URL + 'agents/:id/answeredpercenttoday'
            },
            getTotalGiftAmountToday: {
                method: 'GET',
                url: APP_SETTINGS.BASE_API_URL + 'agents/:id/totalgiftamounttoday'
            },
            getAvgGiftPerCallToday: {
                method: 'GET',
                url: APP_SETTINGS.BASE_API_URL + 'agents/:id/avggiftpercalltoday'
            },
            getEvents: {
                method: 'GET',
                url: APP_SETTINGS.BASE_API_URL + 'agents/:id/agenteventsbyday',
                isArray: true
            },
            getMyMessages: {
                method: 'GET',
                url: APP_SETTINGS.BASE_API_URL + 'broadcastmessages/:id/getmymessages'
            },
            getCampaigns: {
                method: 'GET',
                isArray: true,
                url: APP_SETTINGS.BASE_API_URL + 'agents/:id/campaigns'
            },
            getCampaign: {
                method: 'GET',
                url: APP_SETTINGS.BASE_API_URL + 'agents/:id/campaigns/:campaignId'
            },
            getDashboardStats: {
                method: 'GET',
                url: APP_SETTINGS.BASE_API_URL + 'agents/:id/dashboardstats'
            },
            getCallHistory: {
                method: 'GET',
                url: APP_SETTINGS.BASE_API_URL + 'agents/:id/callhistory',
                isArray: true
            },
            getSales: {
                method: 'GET',
                url: APP_SETTINGS.BASE_API_URL + 'agents/:id/callhistory/sales',
                isArray: true
            },
            getPledges: {
                method: 'GET',
                url: APP_SETTINGS.BASE_API_URL + 'agents/:id/callhistory/pledges',
                isArray: true
            },
            getRefusals: {
                method: 'GET',
                url: APP_SETTINGS.BASE_API_URL + 'agents/:id/callhistory/refusals',
                isArray: true
            },
            getCallRecords: {
                method: 'GET',
                url: APP_SETTINGS.BASE_API_URL + 'agents/:id/callrecords',
                isArray: true
            },
            getWeeklyGoalsView: {
                method: 'GET',
                url: APP_SETTINGS.BASE_API_URL + 'agents/weeklygoals'
            },
            getWeeklyGoalsV2View: {
                method: 'GET',
                url: APP_SETTINGS.BASE_API_URL + 'agents/weeklygoalsv2'
            }
        });
    })
    .factory('AgentEvents', function ($resource, APP_SETTINGS) {
        return $resource(APP_SETTINGS.BASE_API_URL + 'agentevents/:id', {
            id: '@id'
        }, {
            update: {
                method: 'PUT',
            },
            query: {
                method: 'GET',
                isArray: true
            },
            count: {
                method: 'GET',
                params: {
                    countOnly: true
                }
            },
            getByAgentDay: {
                method: 'GET',
                isArray: true,
                url: APP_SETTINGS.BASE_API_URL + 'agentevents/eventbyday'
            }
        });
    })
    .factory('AgentSkills', function ($resource, APP_SETTINGS) {
        return $resource(APP_SETTINGS.BASE_API_URL + 'agents/:id/skills', {
            id: '@id'
        }, {
            update: {
                method: 'PUT',
            },
            query: {
                method: 'GET',
                isArray: true
            },
            count: {
                method: 'GET',
                params: {
                    countOnly: true
                }
            }
        });
    })
    .factory('AgentCallbacks', function ($resource, APP_SETTINGS) {
        return $resource(APP_SETTINGS.BASE_API_URL + 'agents/:id/callbacks', {
            id: '@id'
        }, {
            update: {
                method: 'PUT',
            },
            query: {
                method: 'GET',
                isArray: true
            },
            count: {
                method: 'GET',
                params: {
                    countOnly: true
                }
            },
            getCurrentCallbacks: {
                method: 'GET',
                params: {
                    currentOnly: true
                },
                isArray: true
            },
            getTodaysCallbacks: {
                method: 'GET',
                params: {
                    todayOnly: true
                },
                isArray: true
            },
            getTomorrowsCallbacks: {
                method: 'GET',
                params: {
                    tomorrowOnly: true
                },
                isArray: true
            },
            getExpiredCallbacks: {
                method: 'GET',
                params: {
                    expiredOnly: true
                },
                isArray: true
            }
        });
    })
    .factory('Callback', function ($resource, APP_SETTINGS) {
        return $resource(APP_SETTINGS.BASE_API_URL + 'callbacks/:id', {
            id: '@id'
        }, {
            update: {
                method: 'PUT'
            },
            getExpired: {
                method: 'GET',
                url: APP_SETTINGS.BASE_API_URL + 'callbacks/expired',
                isArray: true
            },
            search: {
                method: 'GET',
                url: APP_SETTINGS.BASE_API_URL + 'callbacks/page',
                params: {
                    page: ':page',
                    orderby: ':orderby',
                    dir: ':dir',
                    filters: ':filters'
                }
            }
        });
    })
    .factory('Lead', function ($resource, APP_SETTINGS) {
        return $resource(APP_SETTINGS.BASE_API_URL + 'leads/:id', {
            id: '@id'
        }, {
            update: {
                method: 'PUT'
            },
            getCallHistory: {
                method: 'GET',
                url: APP_SETTINGS.BASE_API_URL + 'leads/:id/callhistory',
                isArray: true
            },
            getDbFieldNames: {
                method: 'GET',
                url: APP_SETTINGS.BASE_API_URL + 'leads/dbfieldnames',
                isArray: true
            },
            getAuditHistory: {
                method: 'GET',
                url: APP_SETTINGS.BASE_API_URL + 'leads/:id/audithistory',
                isArray: true
            },
            getCallbacks: {
                method: 'GET',
                url: APP_SETTINGS.BASE_API_URL + 'leads/:id/callbacks',
                isArray: true
            },
            getDetail: {
                method: 'GET',
                url: APP_SETTINGS.BASE_API_URL + 'leads/:id/detail'
            },
            getCallAttempts: {
                method: 'GET',
                url: APP_SETTINGS.BASE_API_URL + 'leads/:id/callattemptanalysis'
            }
        });
    })
    .factory('Skill', function ($resource, APP_SETTINGS) {
        return $resource(APP_SETTINGS.BASE_API_URL + 'skills/:id', {
            id: '@id'
        }, {
            update: {
                method: 'PUT'
            }
        });
    })
    .factory('SubSkill', function ($resource, APP_SETTINGS) {
        return $resource(APP_SETTINGS.BASE_API_URL + 'subskills/:id', {
            id: '@id'
        }, {
            update: {
                method: 'PUT'
            }
        });
    })
    .factory('User', function ($resource, APP_SETTINGS) {
        return $resource(APP_SETTINGS.BASE_API_URL + 'users/:id', {
            id: '@id'
        }, {
            update: {
                method: 'PUT'
            },
            checkPassword: {
                method: 'GET',
                url: APP_SETTINGS.BASE_API_URL + 'users/:id/checkpassword',
                params: {
                    password: ':password'
                }
            },
            sendPassword: {
                method: 'GET',
                url: APP_SETTINGS.BASE_API_URL + 'users/:id/emailpassword'
            },
            keepalive: {
                method: 'POST',
                url: APP_SETTINGS.BASE_API_URL + 'users/:id/keepalive',
                ignoreLoadingBar: true
            },
            logout: {
                method: 'DELETE',
                url: APP_SETTINGS.BASE_API_URL + 'users/:id/keepalive'
            },
            getAllLoggedInUsers: {
                method: 'GET',
                isArray: true,
                url: APP_SETTINGS.BASE_API_URL + 'users/keepalive'
            },
            getAgents: {
                method: 'GET',
                isArray: true,
                url: APP_SETTINGS.BASE_API_URL + 'users/:id/agents'
            },
            getSales: {
                method: 'GET',
                url: APP_SETTINGS.BASE_API_URL + 'users/:id/sales',
                params: {
                    page: ':page',
                    limit: ':limit',
                    orderby: ':orderby',
                    startdate: ':startdate',
                    enddate: ':enddate'
                }
            },
            getPledges: {
                method: 'GET',
                url: APP_SETTINGS.BASE_API_URL + 'users/:id/pledges',
                params: {
                    page: ':page',
                    limit: ':limit',
                    orderby: ':orderby',
                    startdate: ':startdate',
                    enddate: ':enddate'
                }
            },
            getCollections: {
                method: 'GET',
                url: APP_SETTINGS.BASE_API_URL + 'users/:id/collections',
                params: {
                    page: ':page',
                    limit: ':limit',
                    orderby: ':orderby',
                    startdate: ':startdate',
                    enddate: ':enddate'
                }
            }
        });
    })
    .factory('CampaignType', function ($resource, APP_SETTINGS) {
        return $resource(APP_SETTINGS.BASE_API_URL + 'campaigntypes/:id', {
            id: '@id'
        }, {
            update: {
                method: 'PUT'
            }
        });
    })
    .factory('AgentState', function ($resource, APP_SETTINGS) {
        return $resource(APP_SETTINGS.BASE_API_URL + 'agentstates/:id', {
            id: '@id'
        }, {
            update: {
                method: 'PUT'
            }
        });
    })
    .factory('LeadField', function ($resource, APP_SETTINGS) {
        return $resource(APP_SETTINGS.BASE_API_URL + 'leadfields/:id', {
            id: '@id'
        }, {
            update: {
                method: 'PUT'
            }
        });
    })
    .factory('LeadFieldValue', function ($resource, APP_SETTINGS) {
        return $resource(APP_SETTINGS.BASE_API_URL + 'leadfieldvalues/:id', {
            id: '@id'
        }, {
            update: {
                method: 'PUT'
            }
        });
    })
    .factory('DateTimeRuleSet', function ($resource, APP_SETTINGS) {
        return $resource(APP_SETTINGS.BASE_API_URL + 'datetimerulesets/:id', {
            id: '@id'
        }, {
            update: {
                method: 'PUT'
            }
        });
    })
    .factory('CallResult', function ($resource, APP_SETTINGS) {
        return $resource(APP_SETTINGS.BASE_API_URL + 'callresults/:id', {
            id: '@id'
        }, {
            update: {
                method: 'PUT'
            },
            removePledge: {
                method: 'PUT',
                url: APP_SETTINGS.BASE_API_URL + 'callresults/:id/removePledge'
            },
            removeSale: {
                method: 'PUT',
                url: APP_SETTINGS.BASE_API_URL + 'callresults/:id/removeSale'
            }
        });
    })
    .factory('AgentSession', function ($resource, APP_SETTINGS) {
        return $resource(APP_SETTINGS.BASE_API_URL + 'agentsessions/:id', {
            id: '@id'
        }, {
            update: {
                method: 'PUT'
            },
            nextCall: {
                method: 'GET',
                url: APP_SETTINGS.BASE_API_URL + 'agentsessions/:id/nextcall'
            },
            keepAlive: {
                method: 'POST',
                url: APP_SETTINGS.BASE_API_URL + 'agentsessions/:id/keepalive',
                ignoreLoadingBar: true
            },
            rejectLead: {
                method: 'POST',
                url: APP_SETTINGS.BASE_API_URL + 'agentsessions/:id/rejectlead'
            },
            clearLead: {
                method: 'POST',
                url: APP_SETTINGS.BASE_API_URL + 'agentsessions/:id/clearlead'
            },
            addEvent: {
                method: 'POST',
                url: APP_SETTINGS.BASE_API_URL + 'agentsessions/:id/events'
            }
        });
    })
    .factory('LeadAudit', function ($resource, APP_SETTINGS) {
        return $resource(APP_SETTINGS.BASE_API_URL + 'leadaudits/:id', {
            id: '@id'
        }, {
            update: {
                method: 'PUT'
            },
            clearAudits: {
                method: 'PUT',
                url: APP_SETTINGS.BASE_API_URL + 'leadaudits/clearAudits'
            }
        });
    })
    .factory('Disposition', function ($resource, APP_SETTINGS) {
        return $resource(APP_SETTINGS.BASE_API_URL + 'dispositions/:id', {
            id: '@id'
        }, {
            update: {
                method: 'PUT'
            }
        });
    })
    .factory('System', function ($resource, APP_SETTINGS) {
        return $resource(APP_SETTINGS.BASE_API_URL + 'system/:key', {
            key: '@key'
        }, {
            update: {
                method: 'PUT'
            },
            getThreadTaskProgress: {
                params: {
                    taskId: '@taskId'
                },
                method: 'GET',
                url: APP_SETTINGS.BASE_API_URL + 'system/threadtaskprogress/:taskId'
            }
        });
    })
    .factory('Device', function ($resource, APP_SETTINGS) {
        return $resource(APP_SETTINGS.BASE_API_URL + 'devices/:id', {
            id: '@id'
        }, {
            update: {
                method: 'PUT'
            }
        });
    })
    .factory('EmailHistory', function ($resource, APP_SETTINGS) {
        return $resource(APP_SETTINGS.BASE_API_URL + 'emailhistory/:id', {
            id: '@id'
        }, {
            update: {
                method: 'PUT'
            },
            resend: {
                method: 'PUT',
                url: APP_SETTINGS.BASE_API_URL + 'emailhistory/:id/resend'
            }
        });
    })
    .factory('Report', function ($resource, APP_SETTINGS) {
        return $resource(APP_SETTINGS.BASE_API_URL + 'reports/:id', {
            id: '@id'
        }, {
            update: {
                method: 'PUT'
            },
            query: {
                method: 'GET',
                isArray: true
            },
            run: {
                method: 'GET',
                url: APP_SETTINGS.BASE_API_URL + 'reports/:id/run'
            },
            count: {
                method: 'GET',
                params: {
                    countOnly: true
                }
            },
            getSchedules: {
                method: 'GET',
                isArray: true,
                url: APP_SETTINGS.BASE_API_URL + 'reports/:id/schedules'
            },
            getClientReports: {
                method: 'GET',
                isArray: true,
                url: APP_SETTINGS.BASE_API_URL + 'reports/client/:id'
            },
            getClientReportHistory: {
                method: 'GET',
                isArray: true,
                url: APP_SETTINGS.BASE_API_URL + 'reports/:id/history/client/:clientid'
            }
        });
    })
    .factory('ReportSchedule', function ($resource, APP_SETTINGS) {
        return $resource(APP_SETTINGS.BASE_API_URL + 'reportschedules/:id', {
            id: '@id'
        }, {
            update: {
                method: 'PUT'
            },
            query: {
                method: 'GET',
                isArray: true
            }
        });
    })
    .factory('Panic', function ($resource, APP_SETTINGS) {
        return $resource(APP_SETTINGS.BASE_API_URL + 'panicreports/:id', {
            id: '@id'
        }, {
            update: {
                method: 'PUT'
            },
            query: {
                method: 'GET',
                isArray: true
            }
        });
    })
    .factory('ReportModules', function ($resource, APP_SETTINGS) {
        return $resource(APP_SETTINGS.BASE_API_URL + 'reportmodules/:id', {
            id: '@id'
        }, {
            query: {
                method: 'GET',
                isArray: true
            },
            getRelatedModules: {
                method: 'GET',
                url: APP_SETTINGS.BASE_API_URL + 'reportmodules/:id/getrelatedmodules',
                isArray: true
            }
        });
    })
    .factory('ReportHistory', function ($resource, APP_SETTINGS) {
        return $resource(APP_SETTINGS.BASE_API_URL + 'reporthistories/:id', {
            id: '@id'
        }, {
            update: {
                method: 'PUT'
            },
            query: {
                method: 'GET',
                isArray: true
            },
            count: {
                method: 'GET',
                params: {
                    countOnly: true
                }
            },
            emailReport: {
                method: 'POST',
                url: APP_SETTINGS.BASE_API_URL + 'reporthistories/:id/email'
            }
        });
    })
    .factory('ReportHistoryAudit', function ($resource, APP_SETTINGS) {
        return $resource(APP_SETTINGS.BASE_API_URL + 'reporthistoryaudits/:id', {
            id: '@id'
        }, {
            update: {
                method: 'PUT'
            },
            query: {
                method: 'GET',
                isArray: true
            }
        });
    })
    .factory('RefusalReason', function ($resource, APP_SETTINGS) {
        return $resource(APP_SETTINGS.BASE_API_URL + 'refusalreasons/:id', {
            id: '@id'
        }, {
            update: {
                method: 'PUT'
            },
            query: {
                method: 'GET',
                isArray: true
            }
        });
    })
    .factory('PaymentLog', function ($resource, APP_SETTINGS) {
        return $resource(APP_SETTINGS.BASE_API_URL + 'paymentlog/:id', {
            id: '@id'
        }, {
            update: {
                method: 'PUT'
            }
        });
    })
    .factory('RecurringPayment', function ($resource, APP_SETTINGS) {
        return $resource(APP_SETTINGS.BASE_API_URL + 'recurringpayment/:id', {
            id: '@id'
        }, {
            update: {
                method: 'PUT'
            }
        });
    });